openapi: 3.0.3
info:
  title: 'CU Api'
  description: |
    This api is a private api that won't be published to the api registry
  version: 1.0.0
tags:
  - name: Hello World
    description: Endpoints to retrieve the hello world message

paths:
  /config/keycloak:
    get:
      tags:
        - Config
      operationId: getKeycloakConfig
      responses:
        '200':
          description: Configuration of Keycloak
          content:
            application/json:
              schema:
                $ref: './model.yaml#/components/schemas/KeycloakConfigResponse'
  /api/requests/{requestId}:
    get:
      tags:
        - Request Information
      operationId: getRequestBasicInfo
      description: Retrieve basic information for a specific request
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      responses:
        '200':
          description: Basic request information successfully retrieved
          content:
            application/json:
              schema:
                $ref: './model.yaml#/components/schemas/RequestBasicInfoResponse'
        '404':
          description: Request not found
  /api/requests/{requestId}/{taskCode}/close:
    put:
      tags:
        - Request Information
      operationId: closeRequestTask
      description: Close a specific task associated with a request
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
        - name: taskCode
          in: path
          required: true
          schema:
            type: string
          description: The code of the task to close
      responses:
        '201':
          description: Task successfully closed - data for next task is returned
          content:
            application/json:
              schema:
                $ref: './model.yaml#/components/schemas/WaveTaskResponse'
        '400':
          description: Invalid request body
        '404':
          description: Request not found
        '422':
          description: Validation error
  /api/requests/{requestId}/assign-user:
    put:
      tags:
        - Request Information
      operationId: assignTaskToUser
      description: A task will be assigned to the current user
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      responses:
        '204':
          description: Task successfully assigned
        '404':
          description: Request not found
  /api/requests/{requestId}/citizen-information:
    get:
      tags:
        - Citizen Information
      operationId: getCitizenInformation
      description: Retrieve employee information for a specific request
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      responses:
        '200':
          description: Employee information successfully retrieved
          content:
            application/json:
              schema:
                $ref: './model.yaml#/components/schemas/CitizenInformationDetailResponse'
        '404':
          description: Request not found
    put:
      tags:
        - Citizen Information
      operationId: updateCitizenInformation
      description: Update mutable employee information for a specific request
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './model.yaml#/components/schemas/UpdateCitizenInformationRequest'
      responses:
        '204':
          description: Employee information successfully updated
        '400':
          description: Invalid request body
        '404':
          description: Request not found
        '422':
          description: Validation error
  /api/requests/{requestId}/mode-of-payment:
    get:
      tags:
        - Mode Of Payment
      operationId: getModeOfPayment
      description: Retrieve mode of payment
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      responses:
        '200':
          description: Mode of payment successfully retrieved
          content:
            application/json:
              schema:
                $ref: './model.yaml#/components/schemas/ModeOfPaymentDetailResponse'
        '404':
          description: Request not found
    put:
      tags:
        - Mode Of Payment
      operationId: updateModeOfPayment
      description: Update mutable mode of payment for a specific request
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './model.yaml#/components/schemas/UpdateModeOfPaymentRequest'
      responses:
        '204':
          description: Mode of payment successfully updated
        '400':
          description: Invalid request body
        '404':
          description: Request not found
        '422':
          description: Validation error
  /api/requests/{requestId}/union-contribution:
    get:
      tags:
        - Union Contribution
      operationId: getUnionContribution
      description: Retrieve union contribution
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      responses:
        '200':
          description: Union contribution successfully retrieved
          content:
            application/json:
              schema:
                $ref: './model.yaml#/components/schemas/UnionContributionDetailResponse'
        '404':
          description: Request not found
    put:
      tags:
        - Union Contribution
      operationId: updateUnionContribution
      description: Update mutable union contribution for a specific request
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './model.yaml#/components/schemas/UpdateUnionContributionRequest'
      responses:
        '204':
          description: Employee information successfully updated
        '400':
          description: Invalid request body
        '404':
          description: Request not found
        '422':
          description: Validation error
  /api/requests/{requestId}/request-information:
    get:
      tags:
        - Request Information
      operationId: getRequestInformation
      description: Retrieve request information for a specific request
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      responses:
        '200':
          description: Request information successfully retrieved
          content:
            application/json:
              schema:
                $ref: './model.yaml#/components/schemas/RequestInformationResponse'
        '404':
          description: Request not found
    put:
      tags:
        - Request Information
      operationId: updateRequestInformation
      description: Update mutable request information for a specific request
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './model.yaml#/components/schemas/UpdateRequestInformationRequest'
      responses:
        '204':
          description: Request information successfully updated
        '400':
          description: Invalid request body
        '404':
          description: Request not found
        '422':
          description: Validation error

  /api/requests/{requestId}/historical/citizen-information/ONEM:
    get:
      tags:
        - Historical Information
      operationId: getHistoricalCitizenOnem
      description: Retrieve citizen information from a historical source (ONEM)
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      responses:
        '200':
          description: Citizen information successfully retrieved
          content:
            application/json:
              schema:
                $ref: './model.yaml#/components/schemas/HistoricalCitizenOnemResponse'
        '404':
          description: Request not found
  /api/requests/{requestId}/historical/citizen-information/C1:
    get:
      tags:
        - Historical Information
      operationId: getHistoricalCitizenC1
      description: Retrieve citizen information from a historical source (C1)
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      responses:
        '200':
          description: Citizen information successfully retrieved
          content:
            application/json:
              schema:
                $ref: './model.yaml#/components/schemas/HistoricalCitizenC1Response'
        '404':
          description: Request not found
  /api/requests/{requestId}/historical/citizen-information/AUTHENTIC_SOURCES:
    get:
      tags:
        - Historical Information
      operationId: getHistoricalCitizenAuthenticSources
      description: Retrieve citizen information from a historical source (C1)
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      responses:
        '200':
          description: Citizen information successfully retrieved
          content:
            application/json:
              schema:
                $ref: './model.yaml#/components/schemas/HistoricalCitizenAuthenticSourcesResponse'
        '404':
          description: Request not found
  /api/requests/{requestId}/historical/barema:
    get:
      tags:
        - Historical Information
      operationId: getBarema
      description: Retrieve barema from service or from snapshot
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      responses:
        '200':
          description: Barema information successfully retrieved
          content:
            application/json:
              schema:
                $ref: './model.yaml#/components/schemas/HistoricalBaremaResponse'
        '404':
          description: Request not found

  /api/requests/{requestId}/citizen-information/select:
    put:
      tags:
        - Citizen Information
      operationId: selectCitizenInformationSources
      description: Select sources for individual fields in citizen information
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './model.yaml#/components/schemas/SelectFieldSourcesRequest'
      responses:
        '204':
          description: Field sources successfully selected
        '400':
          description: Invalid request body
        '404':
          description: Request not found
        '422':
          description: Validation error

  # New endpoint for selecting ModeOfPayment field sources
  /api/requests/{requestId}/mode-of-payment/select:
    put:
      tags:
        - Mode Of Payment
      operationId: selectModeOfPaymentSources
      description: Select sources for individual fields in mode of payment
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './model.yaml#/components/schemas/SelectFieldSourcesRequest'
      responses:
        '204':
          description: Field sources successfully selected
        '400':
          description: Invalid request body
        '404':
          description: Request not found
        '422':
          description: Validation error

  # New endpoint for selecting UnionContribution field sources
  /api/requests/{requestId}/union-contribution/select:
    put:
      tags:
        - Union Contribution
      operationId: selectUnionContributionSources
      description: Select sources for individual fields in union contribution
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The UUID of the request
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './model.yaml#/components/schemas/SelectFieldSourcesRequest'
      responses:
        '204':
          description: Field sources successfully selected
        '400':
          description: Invalid request body
        '404':
          description: Request not found
        '422':
          description: Validation error

