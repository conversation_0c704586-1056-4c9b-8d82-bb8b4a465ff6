import {ComponentFixture, TestBed} from "@angular/core/testing";

import {CuDialogComponent} from "./cu-dialog.component";
import {MAT_DIALOG_DATA, MatDialogRef} from "@angular/material/dialog";

describe('CuDialogComponent', () => {
  let component: CuDialogComponent;
  let fixture: ComponentFixture<CuDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CuDialogComponent],
      providers: [
        {provide: MAT_DIALOG_DATA, useValue: {}},
        {provide: MatDialogRef, useValue: {}},
      ],
    })
    .compileComponents();

    fixture = TestBed.createComponent(CuDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
