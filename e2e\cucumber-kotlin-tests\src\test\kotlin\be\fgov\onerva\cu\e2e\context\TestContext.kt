package be.fgov.onerva.cu.e2e.context

import org.springframework.stereotype.Component
import java.util.concurrent.ConcurrentHashMap

/**
 * Thread-safe test context for sharing data between steps
 */
@Component
class TestContext {
    private val data = ConcurrentHashMap<String, Any>()
    
    fun set(key: String, value: Any) {
        data[key] = value
    }
    
    @Suppress("UNCHECKED_CAST")
    fun <T> get(key: String): T? = data[key] as? T
    
    fun getRequired(key: String): Any = 
        data[key] ?: throw IllegalStateException("Required key '$key' not found in test context")
    
    fun clear() = data.clear()
    
    // Convenience methods
    var latestRequestId: String?
        get() = get("latestRequestId")
        set(value) = value?.let { set("latestRequestId", it) } ?: Unit
        
    var latestTaskId: String?
        get() = get("latestTaskId")
        set(value) = value?.let { set("latestTaskId", it) } ?: Unit
}