@use "@onemrvapublic/design-system-theme" as theme;
@use "@onemrvapublic/design-system" as onemrvaMat;
@use "../common/base-web-component.scss" as baseComponent;

.onemrva-theme {
  @include baseComponent.baseCuWebComponent-theme();

  @include onemrvaMat.layout(theme.$onemrva-theme);
  @include theme.tables-theme(theme.$onemrva-theme);
  @include theme.tooltip-theme(theme.$onemrva-theme);

  // for the dialog

  @include theme.dialogs-theme(theme.$onemrva-theme);
  @include theme.cards-theme(theme.$onemrva-theme);
  @include theme.select-theme(theme.$onemrva-theme);
  @include onemrvaMat.selectablebox(theme.$onemrva-theme);
}

@include baseComponent.commonCuWebComponent-styles();


.cdk-column-label {
  font-weight: bold;
}

.S24Action {
  display: inline-block;
  text-align: right;
  float: right;
}