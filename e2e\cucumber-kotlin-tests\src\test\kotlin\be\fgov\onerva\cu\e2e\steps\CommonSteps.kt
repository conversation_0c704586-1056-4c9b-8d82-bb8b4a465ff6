package be.fgov.onerva.cu.e2e.steps

import be.fgov.onerva.cu.e2e.context.TestContext
import io.cucumber.java.After
import io.cucumber.java.Before
import io.cucumber.java.en.Given
import org.slf4j.LoggerFactory
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

/**
 * Common step definitions for setup, teardown and shared functionality
 */
@SpringBootTest
@ActiveProfiles("ci")
class CommonSteps(
    private val testContext: TestContext
) {
    private val logger = LoggerFactory.getLogger(CommonSteps::class.java)

    @Before
    fun setUp() {
        logger.debug("Setting up test scenario")
        testContext.clear()

        // Initialize test data cleanup tracking
        testContext.addTestDataForCleanup("requests", mutableListOf<String>())
        testContext.addTestDataForCleanup("tasks", mutableListOf<String>())
        testContext.addTestDataForCleanup("messages", mutableListOf<String>())
    }

    @After
    fun tearDown() {
        logger.debug("Tearing down test scenario")

        try {
            // Clean up test data in reverse order of creation
            cleanupTestRequests()
            cleanupTestTasks()
            cleanupTestMessages()
        } catch (e: Exception) {
            logger.warn("Error during test cleanup: ${e.message}", e)
        } finally {
            testContext.clear()
        }
    }

    private fun cleanupTestRequests() {
        val requestIds = testContext.getTestDataForCleanup("requests") as? List<String> ?: return
        requestIds.forEach { requestId ->
            try {
                logger.debug("Cleaning up test request: $requestId")
                // Add cleanup logic for requests if needed
                // This could involve calling APIs to delete test data
            } catch (e: Exception) {
                logger.warn("Failed to cleanup request $requestId: ${e.message}")
            }
        }
    }

    private fun cleanupTestTasks() {
        val taskIds = testContext.getTestDataForCleanup("tasks") as? List<String> ?: return
        taskIds.forEach { taskId ->
            try {
                logger.debug("Cleaning up test task: $taskId")
                // Add cleanup logic for tasks if needed
            } catch (e: Exception) {
                logger.warn("Failed to cleanup task $taskId: ${e.message}")
            }
        }
    }

    private fun cleanupTestMessages() {
        val messageIds = testContext.getTestDataForCleanup("messages") as? List<String> ?: return
        messageIds.forEach { messageId ->
            try {
                logger.debug("Cleaning up test message: $messageId")
                // Add cleanup logic for messages if needed
            } catch (e: Exception) {
                logger.warn("Failed to cleanup message $messageId: ${e.message}")
            }
        }
    }

    @Given("the test environment is ready")
    fun theTestEnvironmentIsReady() {
        logger.info("Test environment is ready for scenario execution")
        // This step serves as a background step to ensure the test environment is initialized
        // The actual setup is done in the @Before hook
    }
}
