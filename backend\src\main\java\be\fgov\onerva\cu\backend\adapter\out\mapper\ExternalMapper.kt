package be.fgov.onerva.cu.backend.adapter.out.mapper

import be.fgov.onerva.cu.backend.application.domain.AddressNullable
import be.fgov.onerva.cu.backend.application.domain.Citizen
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenAuthenticSources
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenOnem
import be.fgov.onerva.cu.backend.application.domain.WaveTaskStatus
import be.fgov.onerva.cu.backend.application.exception.InvalidExternalDataException
import be.fgov.onerva.cu.backend.application.exception.WaveTaskStatusException
import be.fgov.onerva.cu.backend.wo.dto.WoTaskDTO
import be.fgov.onerva.person.rest.model.CitizenDTO
import be.fgov.onerva.person.rest.model.CitizenInfoDTO
import be.fgov.onerva.registerproxyservice.rest.model.RegisterAddress
import be.fgov.onerva.registerproxyservice.rest.model.RegisterPerson
import be.fgov.onerva.registerproxyservice.rest.model.ResidentialAddress

fun CitizenDTO.toDomainCitizen() = Citizen(
    firstName = this.firstname,
    lastName = this.lastname,
    numbox = this.numbox,
    zipCode = this.zipCode?.toString() ?: ""
)

fun WoTaskDTO.toDomainTaskStatus() = when (this.status) {
    "OPEN" -> if (this.step == "wait") WaveTaskStatus.WAITING else WaveTaskStatus.OPEN
    "CLOSED" -> WaveTaskStatus.CLOSED
    else -> throw WaveTaskStatusException("Unknown task status: ${this.status}")
}

fun CitizenInfoDTO.toCitizenInfoWithAddress(): HistoricalCitizenOnem {
    if (this.address == null) {
        throw InvalidExternalDataException("Invalid address format: ${this.address}")
    }

    return HistoricalCitizenOnem(
        firstName = this.firstName,
        lastName = this.lastName,
        numbox = this.numBox.toInt(),
        nationality = this.flagNation.toString(),
        iban = this.bankAccount?.iban,
        bic = this.bankAccount?.bic,
        otherPersonName = if (this.paymentMode == 2) this.bankAccount?.holder ?: "$firstName $lastName" else null,
        birthDate = this.birthDate,
        authorized = if (this.unionDue != null && this.unionDue.mandateActive != null) this.unionDue.mandateActive else null,
        effectiveDate = if (this.unionDue != null && this.unionDue.validFrom != null) this.unionDue.validFrom else null,
        bankAccountValueDate = if (this.bankAccount != null) this.bankAccount.validFrom else null,
        address = AddressNullable(
            street = this.addressObj.street,
            houseNumber = this.addressObj.number,
            boxNumber = this.addressObj.box,
            zipCode = this.addressObj.zip,
            country = this.addressObj.countryCode?.toString(),
            city = this.addressObj.city,
            valueDate = this.addressObj.validFrom,
        ),
        paymentMode = this.paymentMode,
    )
}

fun RegisterPerson.toCitizenInfoWithAddress(): HistoricalCitizenAuthenticSources {
    val primaryName = this.names.firstOrNull { it.seq == 1 }
        ?: throw InvalidExternalDataException("No name with sequence number 1 found")

    val primaryNationality = this.nationalities.firstOrNull()
        ?: throw InvalidExternalDataException("No nationality found")

    val residentialAddress =
        addresses.firstOrNull { it.atType == RegisterAddress.AtTypeEnum.RESIDENTIAL_ADDRESS && it.radiated == false && it.validityPeriod.beginDate != null } as? ResidentialAddress
            ?: throw InvalidExternalDataException("No residential address found")

    val valueDateTimestamp = residentialAddress.validityPeriod.beginDate!!
    val valueDate = valueDateTimestamp.let {
        java.time.Instant.ofEpochMilli(it)
            .atZone(java.time.ZoneId.systemDefault())
            .toLocalDate()
    }

    return HistoricalCitizenAuthenticSources(
        firstName = primaryName.firstName,
        lastName = this.lastName,
        nationality = primaryNationality.nationalityCode.toString(),
        birthDate = this.birthdate,
        address = AddressNullable(
            street = "",
            houseNumber = residentialAddress.houseNumber,
            boxNumber = residentialAddress.boxNumber,
            zipCode = residentialAddress.postalCode,
            country = null,
            city = null,
            valueDate = valueDate,
        ),
    )
}

