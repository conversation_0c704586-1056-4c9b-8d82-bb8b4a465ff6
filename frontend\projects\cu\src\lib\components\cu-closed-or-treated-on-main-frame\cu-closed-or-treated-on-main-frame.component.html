<div *ngIf="showMainFrameMessage()">
    <div class="taskMessage status-ok" *ngIf="pushbackStatus=='OK'">
        <mat-icon class="filled" color="success">check_circle
        </mat-icon>
        {{ 'MAINFRAME_RESPONSE.OK' | translate }}
    </div>
    <div class="taskMessage status-nok" *ngIf="pushbackStatus=='NOK'">
        <mat-icon class="filled" color="error">error_circle
        </mat-icon>
        {{ 'MAINFRAME_RESPONSE.NOK' | translate }}
    </div>
    <div class="taskMessage status-pending" *ngIf="pushbackStatus=='PENDING'">
        <mat-icon class="filled" color="warning">warning_circle
        </mat-icon>
        {{ 'MAINFRAME_RESPONSE.PENDING' | translate }}
    </div>
</div>

<div *ngIf="isTreatedOnMainFrame()" class="taskMessage success-message">
    <mat-icon class="filled" color="success">check_circle_outline_outlined
    </mat-icon>
    {{ 'TREATED_ON_MAINFRAME.CLOSED' | translate }} {{ 'TREATED_ON_MAINFRAME.DECISION' | translate }} {{ 'TREATED_ON_MAINFRAME.CODE.' + decisionType | translate }} {{ 'TREATED_ON_MAINFRAME.BAREMA' | translate }} {{ decisionBarema || "N/A" }} {{ 'TREATED_ON_MAINFRAME.VALIDATED' | translate }}
</div>

<div *ngIf="isClosedAndNotTreated()" class="taskMessage">
    <mat-icon class="filled" color="success">check_circle</mat-icon>
    {{ nextTaskDescription | translate }} {{ nextTaskAction | translate }}
</div>