package be.fgov.onerva.cu.backend.application.service.helpers

import java.util.UUID
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.application.domain.CreateChangePersonalDataTaskCommand
import be.fgov.onerva.cu.backend.application.domain.WaveTask
import be.fgov.onerva.cu.backend.application.port.out.CitizenInformationPort
import be.fgov.onerva.cu.backend.application.port.out.ModeOfPaymentPort
import be.fgov.onerva.cu.backend.application.port.out.RequestInformationPort
import be.fgov.onerva.cu.backend.application.port.out.UnionContributionPort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPersistencePort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPort
import be.fgov.onerva.cu.common.aop.LogMethodCall
import be.fgov.onerva.cu.common.aop.SensitiveParam
import be.fgov.onerva.cu.common.utils.logger

@Service
class WaveTaskHelper(
    private val waveTaskPersistencePort: WaveTaskPersistencePort,
    private val waveTaskPort: WaveTaskPort,
    private val citizenInformationPort: CitizenInformationPort,
    private val modeOfPaymentPort: ModeOfPaymentPort,
    private val unionContributionPort: UnionContributionPort,
    private val requestInformationPort: RequestInformationPort,
) {
    private val log = logger

    fun createChangePersonalDataTask(
        requestId: UUID,
        createChangePersonalDataTaskCommand: CreateChangePersonalDataTaskCommand,
    ) {

        val waveTask = waveTaskPort.createChangePersonalDataTask(requestId, createChangePersonalDataTaskCommand)

        waveTaskPersistencePort.persistChangePersonalDataCaptureWaveTask(requestId, waveTask)
    }

    fun assignAndCloseDataCaptureTask(
        requestId: UUID,
        taskId: String,
        assignee: String,
    ): Boolean {
        waveTaskPort.assignTaskToUser(taskId, assignee)
        val taskWasClosed = waveTaskPort.closeTask(taskId)

        if (taskWasClosed) {
            val citizenInformationLatestRevision = citizenInformationPort.getLatestRevision(requestId)
            val modeOfPaymentLatestRevision = modeOfPaymentPort.getLatestRevision(requestId)
            val unionContributionLatestRevision = unionContributionPort.getLatestRevision(requestId)
            val requestInformationLatestRevision = requestInformationPort.getLatestRevision(requestId)

            waveTaskPersistencePort.closeWaveTaskChangePersonalDataCapture(
                requestId,
                citizenInformationLatestRevision,
                modeOfPaymentLatestRevision,
                unionContributionLatestRevision,
                requestInformationLatestRevision,
            )
        } else {
            log.warn("Task DataCaptureTask for request $requestId was already closed")
        }
        return taskWasClosed
    }

    fun assignAndSleepDataCaptureTask(
        requestId: UUID,
        taskId: String,
        assignee: String,
    ): Boolean {
        waveTaskPort.assignTaskToUser(taskId, assignee)
        val taskWasClosed = waveTaskPort.sleepTask(taskId)

        if (taskWasClosed) {
            val citizenInformationLatestRevision = citizenInformationPort.getLatestRevision(requestId)
            val modeOfPaymentLatestRevision = modeOfPaymentPort.getLatestRevision(requestId)
            val unionContributionLatestRevision = unionContributionPort.getLatestRevision(requestId)
            val requestInformationLatestRevision = requestInformationPort.getLatestRevision(requestId)

            waveTaskPersistencePort.sleepWaveTaskChangePersonalDataCapture(
                requestId,
                citizenInformationLatestRevision,
                modeOfPaymentLatestRevision,
                unionContributionLatestRevision,
                requestInformationLatestRevision,
            )
        } else {
            log.warn("Task DataCaptureTask for request $requestId was already made to sleep")
        }
        return taskWasClosed
    }

    fun createChangePersonalDataValidateTask(
        requestId: UUID,
        processId: String,
        createChangePersonalDataTaskCommand: CreateChangePersonalDataTaskCommand,
        assignee: String,
    ): WaveTask? {

        val waveTask = waveTaskPort.createChangePersonalDataValidateTask(
            requestId, processId, assignee, createChangePersonalDataTaskCommand
        )

        // Save task information
        waveTaskPersistencePort.persistChangePersonalDataValidateWaveTask(
            requestId,
            waveTask,
        )
        return waveTask
    }

    fun assignAndCloseValidateDataTask(
        requestId: UUID,
        taskId: String,
        assignee: String,
    ) {
        waveTaskPort.assignTaskToUser(taskId, assignee)
        val taskWasClosed = waveTaskPort.closeTask(taskId)

        if (taskWasClosed) {
            val citizenInformationLatestRevision = citizenInformationPort.getLatestRevision(requestId)
            val modeOfPaymentLatestRevision = modeOfPaymentPort.getLatestRevision(requestId)
            val unionContributionLatestRevision = unionContributionPort.getLatestRevision(requestId)
            val requestInformationLatestRevision = requestInformationPort.getLatestRevision(requestId)

            waveTaskPersistencePort.closeWaveTaskChangePersonalDataValidate(
                requestId,
                citizenInformationLatestRevision,
                modeOfPaymentLatestRevision,
                unionContributionLatestRevision,
                requestInformationLatestRevision,
            )
        } else {
            log.warn("Task ValidateDataTask for request $requestId was already closed")
        }
    }

    fun assignAndSleepValidateDataTask(
        requestId: UUID,
        taskId: String,
        assignee: String,
    ): Boolean {
        waveTaskPort.assignTaskToUser(taskId, assignee)
        val taskWasClosed = waveTaskPort.sleepTask(taskId)

        if (taskWasClosed) {
            val citizenInformationLatestRevision = citizenInformationPort.getLatestRevision(requestId)
            val modeOfPaymentLatestRevision = modeOfPaymentPort.getLatestRevision(requestId)
            val unionContributionLatestRevision = unionContributionPort.getLatestRevision(requestId)
            val requestInformationLatestRevision = requestInformationPort.getLatestRevision(requestId)

            waveTaskPersistencePort.sleepWaveTaskChangePersonalDataValidate(
                requestId,
                citizenInformationLatestRevision,
                modeOfPaymentLatestRevision,
                unionContributionLatestRevision,
                requestInformationLatestRevision,
            )
        } else {
            log.warn("Task ValidateDataTask for request $requestId was already set to sleep")
        }
        return taskWasClosed
    }

    @LogMethodCall
    fun assignTaskToUser(requestId: UUID, @SensitiveParam currentUsername: String) {
        // figure out what task is open with given requestId.
        val task = waveTaskPersistencePort.getOpenWaveTaskByRequestId(requestId)

        waveTaskPort.assignTaskToUser(task.taskId, currentUsername)
    }
}