<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>be.fgov.onerva.cu</groupId>
  <artifactId>cu-root</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>Root CU Project</name>
  <modules>
    <module>backend</module>
    <module>bff</module>
    <module>common</module>
    <module>e2e/cucumber-kotlin-tests</module>
  </modules>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    
    <java.version>21</java.version>
    <kotlin.version>2.1.0</kotlin.version>
    <maven.compiler.source>${java.version}</maven.compiler.source>
    <maven.compiler.target>${java.version}</maven.compiler.target>
    <maven.compiler.release>${java.version}</maven.compiler.release>

    <archunit-junit5.version>1.2.1</archunit-junit5.version>
    <commons-collections4.version>4.4</commons-collections4.version>
    <commons-io.version>2.16.1</commons-io.version>
    <commons-validator.version>1.9.0</commons-validator.version>
    <flagsmith-java-client.version>7.4.3</flagsmith-java-client.version>
    <hibernate-envers.version>6.5.3.Final</hibernate-envers.version>
    <jackson-module-kotlin.version>2.18.2</jackson-module-kotlin.version>
    <jacoco-maven-plugin.version>0.8.11</jacoco-maven-plugin.version>
    <jreleaser-maven-plugin.version>1.15.0</jreleaser-maven-plugin.version>
    <kotlinx-coroutines.version>1.10.1</kotlinx-coroutines.version>
    <maven-failsafe-plugin.version>3.2.5</maven-failsafe-plugin.version>
    <maven-surefire-plugin.version>3.2.5</maven-surefire-plugin.version>
    <mockk.version>1.13.17</mockk.version>
    <observability-spring-boot-starter.version>4.0</observability-spring-boot-starter.version>
    <rabbitmq-consumer-autoconfigure.version>3.0.1</rabbitmq-consumer-autoconfigure.version>
    <sonar.ci.autoconfig.disabled>true</sonar.ci.autoconfig.disabled>
    <spring-boot-dependencies.version>3.4.3</spring-boot-dependencies.version>
    <spring-boot-maven-plugin.version>3.4.3</spring-boot-maven-plugin.version>
    <spring-cloud-dependencies.version>2024.0.0</spring-cloud-dependencies.version>
    <spring-data-envers.version>3.4.1</spring-data-envers.version>
    <springdoc.version>1.6.14</springdoc.version>
    <springmockk.version>4.0.2</springmockk.version>
    <commons-lang3.version>3.17.0</commons-lang3.version>
    <cucumber-bom.version>7.22.0</cucumber-bom.version>
    <openfeature-sdk.version>1.15.1</openfeature-sdk.version>
    <flagsmith-provider.version>0.0.10</flagsmith-provider.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <!-- Spring Boot Dependencies -->
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring-boot-dependencies.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <dependency>
        <groupId>io.cucumber</groupId>
        <artifactId>cucumber-bom</artifactId>
        <version>${cucumber-bom.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <!-- Spring Cloud Dependencies -->
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>${spring-cloud-dependencies.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <dependency>
        <groupId>be.fgov.onerva.observability</groupId>
        <artifactId>observability-spring-boot-starter</artifactId>
        <version>${observability-spring-boot-starter.version}</version>
      </dependency>

      <dependency>
        <groupId>org.springdoc</groupId>
        <artifactId>springdoc-openapi-ui</artifactId>
        <version>${springdoc.version}</version>
      </dependency>
      <dependency>
        <groupId>org.openapitools</groupId>
        <artifactId>jackson-databind-nullable</artifactId>
        <version>${jackson-databind-nullable.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-kotlin</artifactId>
        <version>${jackson-module-kotlin.version}</version>
      </dependency>

      <dependency>
        <groupId>be.fgov.onerva.rabbitmq-consumer</groupId>
        <artifactId>rabbitmq-consumer-autoconfigure</artifactId>
        <version>${rabbitmq-consumer-autoconfigure.version}</version>
      </dependency>

      <dependency>
        <groupId>com.flagsmith</groupId>
        <artifactId>flagsmith-java-client</artifactId>
        <version>${flagsmith-java-client.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-collections4</artifactId>
        <version>${commons-collections4.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>${commons-lang3.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>${commons-io.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-validator</groupId>
        <artifactId>commons-validator</artifactId>
        <version>${commons-validator.version}</version>
      </dependency>

      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-envers</artifactId>
        <version>${hibernate-envers.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-envers</artifactId>
        <version>${spring-data-envers.version}</version>
      </dependency>


      <!-- Flagsmith + Open Feature-->
      <dependency>
        <groupId>dev.openfeature</groupId>
        <artifactId>sdk</artifactId>
        <version>${openfeature-sdk.version}</version>
      </dependency>
      <dependency>
        <groupId>dev.openfeature.contrib.providers</groupId>
        <artifactId>flagsmith</artifactId>
        <version>${flagsmith-provider.version}</version>
      </dependency>

      <!-- Kotlin -->
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib</artifactId>
        <version>${kotlin.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib-jdk8</artifactId>
        <version>${kotlin.version}</version>
      </dependency>

      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-reflect</artifactId>
        <version>${kotlin.version}</version>
      </dependency>

      <!-- Coroutines -->
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-core</artifactId>
        <version>${kotlinx-coroutines.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-reactor</artifactId>
        <version>${kotlinx-coroutines.version}</version>
      </dependency>


      <dependency>
        <groupId>io.mockk</groupId>
        <artifactId>mockk-jvm</artifactId>
        <version>${mockk.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.ninja-squad</groupId>
        <artifactId>springmockk</artifactId>
        <version>${springmockk.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.tngtech.archunit</groupId>
        <artifactId>archunit-junit5</artifactId>
        <version>${archunit-junit5.version}</version>
        <scope>test</scope>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.jacoco</groupId>
          <artifactId>jacoco-maven-plugin</artifactId>
          <version>${jacoco-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.jreleaser</groupId>
          <artifactId>jreleaser-maven-plugin</artifactId>
          <version>${jreleaser-maven-plugin.version}</version>
          <configuration>
            <configFile>jreleaser.yml</configFile>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-maven-plugin</artifactId>
          <version>${spring-boot-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${maven-surefire-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-failsafe-plugin</artifactId>
          <version>${maven-failsafe-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.12.1</version>
        </plugin>
        <plugin>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-maven-plugin</artifactId>
          <version>2.1.0</version>
        </plugin>
        <plugin>
          <groupId>com.google.cloud.tools</groupId>
          <artifactId>jib-maven-plugin</artifactId>
          <version>3.4.2</version>
        </plugin>
        <plugin>
          <groupId>org.openapitools</groupId>
          <artifactId>openapi-generator-maven-plugin</artifactId>
          <version>7.9.0</version>
        </plugin>
        <plugin>
          <groupId>io.apicurio</groupId>
          <artifactId>apicurio-registry-maven-plugin</artifactId>
          <version>2.4.2.Final</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.jreleaser</groupId>
        <artifactId>jreleaser-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>
