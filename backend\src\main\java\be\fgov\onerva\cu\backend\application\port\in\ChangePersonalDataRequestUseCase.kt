package be.fgov.onerva.cu.backend.application.port.`in`

import java.util.UUID
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequestReceivedCommand
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequestTreatedCommand

/**
 * Use case interface for processing received change of address requests.
 * This interface defines the entry point for handling incoming change of address notifications.
 */
@FunctionalInterface
interface ChangePersonalDataRequestUseCase {
    /**
     * Processes a received change of address notification.
     * This method handles the business logic for incoming change of address requests.
     *
     * @param changePersonalDataRequestReceivedCommand The received change of address data to be processed
     */
    fun receivedChangePersonalData(changePersonalDataRequestReceivedCommand: ChangePersonalDataRequestReceivedCommand): UUID?

    /**
     * Processes a change of address request that has been treated in the MFx.
     *
     * @param changePersonalDataTreated The request that has been treated
     */
    fun processTreatedChangePersonalData(changePersonalDataTreated: ChangePersonalDataRequestTreatedCommand)
}