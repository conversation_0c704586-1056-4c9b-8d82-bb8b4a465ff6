package be.fgov.onerva.cu.backend.adapter.`in`.mapper

import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.assertj.core.groups.Tuple.tuple
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import be.fgov.onerva.cu.backend.application.domain.AddressNullable
import be.fgov.onerva.cu.backend.application.domain.Annex
import be.fgov.onerva.cu.backend.application.domain.AnnexType
import be.fgov.onerva.cu.backend.application.domain.CitizenInformation
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenAuthenticSources
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenC1
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenOnem
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.domain.RequestBasicInfo
import be.fgov.onerva.cu.backend.application.domain.RequestInformation
import be.fgov.onerva.cu.backend.application.domain.SyncFollowUpStatus
import be.fgov.onerva.cu.backend.application.domain.UnionContribution
import be.fgov.onerva.cu.backend.application.domain.WaveTask
import be.fgov.onerva.cu.backend.application.domain.WaveTaskStatus
import be.fgov.onerva.cu.backend.application.exception.InvalidInputException
import be.fgov.onerva.cu.rest.priv.model.Address
import be.fgov.onerva.cu.rest.priv.model.RequestBasicInfoResponse
import be.fgov.onerva.cu.rest.priv.model.UpdateCitizenInformationRequest
import be.fgov.onerva.cu.rest.priv.model.UpdateModeOfPaymentRequest
import be.fgov.onerva.cu.rest.priv.model.UpdateRequestInformationRequest

class ControllerMapperTest {

    @Nested
    inner class UpdateCitizenInformationRequestMapper {

        @Test
        fun `toUpdateCitizenInformationReceived should map all fields correctly`() {
            // Given
            val request = UpdateCitizenInformationRequest(
            ).apply {
                nationality = "Belgian"
                birthDate = LocalDate.of(1990, 1, 1)
                address = Address(
                    "Belgium",
                    "Main Street",
                    "42",
                    "1000",
                    "Brussels",
                )

            }

            // When
            val result = request.toDomainUpdateCitizenInformation()

            // Then
            assertThat(result.birthDate).isEqualTo(LocalDate.of(1990, 1, 1))
            assertThat(result.nationality).isEqualTo("Belgian")
            assertThat(result.address).extracting(
                "country",
                "street",
                "houseNumber",
                "city",
                "zipCode"
            ).containsExactly(
                "Belgium",
                "Main Street",
                "42",
                "Brussels",
                "1000"
            )
        }
    }

    @Nested
    inner class CitizenInformationMapper {

        @Test
        fun `toCitizenInformationDetailResponse should map all fields correctly`() {
            // Given
            val employeeInfo = CitizenInformation(
                firstName = "John",
                lastName = "Doe",
                birthDate = LocalDate.of(1990, 1, 1),
                nationality = "Belgian",
                address = be.fgov.onerva.cu.backend.application.domain.Address(
                    country = "Belgium",
                    street = "Main Street",
                    houseNumber = "42",
                    boxNumber = "A",
                    city = "Brussels",
                    zipCode = "1000"
                )
            )

            // When
            val result = employeeInfo.toCitizenInformationDetailResponse()

            // Then
            assertThat(result.birthDate).isEqualTo(LocalDate.of(1990, 1, 1))
            assertThat(result.nationality).isEqualTo("Belgian")
            assertThat(result.address)
                .extracting(
                    be.fgov.onerva.cu.rest.priv.model.Address::getCountry,
                    be.fgov.onerva.cu.rest.priv.model.Address::getStreet,
                    be.fgov.onerva.cu.rest.priv.model.Address::getHouseNumber,
                    be.fgov.onerva.cu.rest.priv.model.Address::getBoxNumber,
                    be.fgov.onerva.cu.rest.priv.model.Address::getCity,
                    be.fgov.onerva.cu.rest.priv.model.Address::getZipCode,
                ).containsExactly(
                    "Belgium",
                    "Main Street",
                    "42",
                    "A",
                    "Brussels",
                    "1000"
                )
        }
    }

    @Nested
    inner class ModeOfPaymentRequestMapper {

        @Test
        fun `toModeOfPayment should map belgian bank account correctly`() {
            // Given
            val request = UpdateModeOfPaymentRequest().apply {
                iban = "****************"
            }

            // When
            val result = request.toDomainUpdateModeOfPayment()

            // Then
            assertThat(result.otherPersonName).isNull()
            assertThat(result.otherPersonName).isNull()
            assertThat(result.iban).isEqualTo("****************")
            assertThat(result.bic).isNull()
        }

        @Test
        fun `toModeOfPayment should map foreign bank account correctly`() {
            // Given
            val request = UpdateModeOfPaymentRequest().apply {
                iban = "FR763000600001*********0189"
                bic = "BNPAFRPP"
            }

            // When
            val result = request.toDomainUpdateModeOfPayment()

            // Then
            assertThat(result.otherPersonName).isNull()
            assertThat(result.iban).isEqualTo("FR763000600001*********0189")
            assertThat(result.bic).isEqualTo("BNPAFRPP")
        }

        @Test
        fun `toModeOfPayment should map other person bank account correctly`() {
            // Given
            val request = UpdateModeOfPaymentRequest().apply {
                otherPersonName = "John Doe"
                iban = "****************"
            }

            // When
            val result = request.toDomainUpdateModeOfPayment()

            // Then
            assertThat(result.otherPersonName).isEqualTo("John Doe")
            assertThat(result.iban).isEqualTo("****************")
            assertThat(result.bic).isNull()
        }

        @Test
        fun `toDomainModeOfPayment should throw exception when no bank account is provided`() {
            // Given
            val request = UpdateModeOfPaymentRequest()

            // When/Then
            assertThatThrownBy { request.toDomainUpdateModeOfPayment() }
                .isInstanceOf(InvalidInputException::class.java)
                .hasMessage("Bank account is required")
        }
    }

    @Nested
    inner class ModeOfPaymentMapper {

        @Test
        fun `toModeOfPaymentDetailResponse should map belgian bank account correctly`() {
            // Given
            val modeOfPayment = ModeOfPayment(
                otherPersonName = null,
                iban = "****************",
                bic = null,
            )

            // When
            val result = modeOfPayment.toModeOfPaymentDetailResponse()

            // Then
            assertThat(result.otherPersonName).isNull()
            assertThat(result.iban).isEqualTo("****************")
        }

        @Test
        fun `toModeOfPaymentDetailResponse should map foreign bank account correctly`() {
            // Given
            val modeOfPayment = ModeOfPayment(
                otherPersonName = null,
                iban = "FR763000600001*********0189",
                bic = "BNPAFRPP",
            )

            // When
            val result = modeOfPayment.toModeOfPaymentDetailResponse()

            // Then
            assertThat(result.otherPersonName).isNull()
            assertThat(result.iban).isEqualTo("FR763000600001*********0189")
            assertThat(result.bic).isEqualTo("BNPAFRPP")
        }

        @Test
        @Disabled
        fun `toModeOfPaymentDetailResponse should map other person bank account correctly`() {
            // Given
            val modeOfPayment = ModeOfPayment(
                otherPersonName = "John Doe",
                iban = "****************",
                bic = null,
            )

            // When
            val result = modeOfPayment.toModeOfPaymentDetailResponse()

            // Then
            assertThat(result.otherPersonName).isEqualTo("John Doe")
            assertThat(result.iban).isEqualTo("****************")
            assertThat(result.bic).isNull()
        }
    }

    @Nested
    inner class RequestBasicInfoMapperTest {

        @Test
        fun `should map RequestBasicInfo to RequestBasicInfoResponse with all fields`() {
            // Given
            val requestBasicInfo = RequestBasicInfo(
                requestDate = LocalDate.of(2024, 12, 16),
                ssin = "*********",
                firstName = "John",
                lastName = "Doe",
                introductionDate = LocalDate.of(2024, 12, 15),
                dateValid = LocalDate.of(2024, 12, 15),
                annexes = listOf(
                    Annex(
                        url = "http://the-scan-url",
                        type = AnnexType.SCANNED_DOCUMENTS
                    )
                ),
                decisionType = null,
                decisionBarema = null,
                c9Id = 12345,
                pushbackStatus = SyncFollowUpStatus.PENDING
            )

            // When
            val response = requestBasicInfo.toRequestBasicInfoResponse()

            // Then
            assertThat(response)
                .isNotNull()
                .extracting(
                    "requestDate",
                    "ssin",
                    "firstName",
                    "lastName",
                    "introductionDate",
                    "dateValid",
                    "annexes",
                    "c9Id",
                    "pushbackStatus"
                )
                .containsExactly(
                    LocalDate.of(2024, 12, 16),
                    "*********",
                    "John",
                    "Doe",
                    LocalDate.of(2024, 12, 15),
                    LocalDate.of(2024, 12, 15),
                    listOf(
                        be.fgov.onerva.cu.rest.priv.model.Annex()
                            .type(be.fgov.onerva.cu.rest.priv.model.AnnexType.SCANNED_DOCUMENTS)
                            .url("http://the-scan-url")
                    ),
                    "12345",
                    RequestBasicInfoResponse.PushbackStatusEnum.PENDING
                )
        }

        @Test
        fun `should map RequestBasicInfo with empty strings`() {
            // Given
            val requestBasicInfo = RequestBasicInfo(
                requestDate = LocalDate.of(2024, 12, 16),
                ssin = "",
                firstName = "",
                lastName = "",
                introductionDate = LocalDate.of(2024, 12, 15),
                dateValid = LocalDate.of(2024, 12, 15),
                annexes = listOf(
                    Annex(
                        url = "http://the-scan-url",
                        type = AnnexType.SCANNED_DOCUMENTS
                    )
                ),
                decisionType = null,
                decisionBarema = null,
                c9Id = 12345,
                pushbackStatus = null,
            )

            // When
            val response = requestBasicInfo.toRequestBasicInfoResponse()

            // Then
            assertThat(response)
                .isNotNull()
                .extracting(
                    "requestDate",
                    "ssin",
                    "firstName",
                    "lastName",
                    "introductionDate",
                    "dateValid",
                    "annexes",
                    "c9Id"
                )
                .containsExactly(
                    LocalDate.of(2024, 12, 16),
                    "",
                    "",
                    "",
                    LocalDate.of(2024, 12, 15),
                    LocalDate.of(2024, 12, 15),
                    listOf(
                        be.fgov.onerva.cu.rest.priv.model.Annex()
                            .type(be.fgov.onerva.cu.rest.priv.model.AnnexType.SCANNED_DOCUMENTS)
                            .url("http://the-scan-url")
                    ),
                    "12345"
                )
        }

        @Test
        fun `should preserve identical values during mapping`() {
            // Given
            val requestBasicInfo = RequestBasicInfo(
                requestDate = LocalDate.of(2024, 12, 16),
                ssin = "*********",
                firstName = "John",
                lastName = "Doe",
                introductionDate = LocalDate.of(2024, 12, 15),
                dateValid = LocalDate.of(2024, 12, 15),
                annexes = listOf(
                    Annex(
                        url = "http://the-scan-url",
                        type = AnnexType.SCANNED_DOCUMENTS
                    )
                ),
                decisionType = null,
                decisionBarema = null,
                c9Id = 12345,
                pushbackStatus = null,
            )

            // When
            val response = requestBasicInfo.toRequestBasicInfoResponse()

            // Then
            assertThat(response.requestDate).isEqualTo(requestBasicInfo.requestDate)
            assertThat(response.ssin).isEqualTo(requestBasicInfo.ssin)
            assertThat(response.firstName).isEqualTo(requestBasicInfo.firstName)
            assertThat(response.lastName).isEqualTo(requestBasicInfo.lastName)
            assertThat(response.introductionDate).isEqualTo(requestBasicInfo.introductionDate)
            assertThat(response.dateValid).isEqualTo(requestBasicInfo.dateValid)
            assertThat(response.c9Id).isEqualTo("12345")
            assertThat(response.annexes).hasSize(1).extracting("url", "type")
                .containsExactly(
                    tuple("http://the-scan-url", be.fgov.onerva.cu.rest.priv.model.AnnexType.SCANNED_DOCUMENTS)
                )
        }
    }

    @Nested
    inner class WaveTaskStatusMapperTest {

        @ParameterizedTest
        @EnumSource(WaveTaskStatus::class)
        fun `toResponseWaveTaskStatus should map all enum values correctly`(status: WaveTaskStatus) {
            // When
            val result = status.toResponseWaveTaskStatus()

            // Then
            when (status) {
                WaveTaskStatus.OPEN -> assertThat(result).isEqualTo(be.fgov.onerva.cu.rest.priv.model.WaveTaskStatus.OPEN)
                WaveTaskStatus.CLOSED -> assertThat(result).isEqualTo(be.fgov.onerva.cu.rest.priv.model.WaveTaskStatus.CLOSED)
                WaveTaskStatus.WAITING -> assertThat(result).isEqualTo(be.fgov.onerva.cu.rest.priv.model.WaveTaskStatus.WAITING)
            }
        }
    }

    @Nested
    inner class WaveTaskMapperTest {

        @Test
        fun `toWaveTaskResponse should map all fields correctly`() {
            // Given
            val waveTask = WaveTask(
                processId = "process123",
                taskId = "task456",
                status = WaveTaskStatus.OPEN
            )

            // When
            val result = waveTask.toWaveTaskResponse()

            // Then
            assertThat(result.processId).isEqualTo("process123")
            assertThat(result.taskId).isEqualTo("task456")
            assertThat(result.status).isEqualTo(be.fgov.onerva.cu.rest.priv.model.WaveTaskStatus.OPEN)
            assertThat(result.waveUrl).isEqualTo("/processes-page/process/(task-detail/process123!!sidemenu:task-detail/task456)")
        }
    }

    @Nested
    inner class RequestInformationMapperTest {

        @Test
        fun `toRequestInformationResponse should map fields correctly`() {
            // Given
            val requestDate = LocalDate.of(2024, 3, 15)
            val requestInformation = RequestInformation(requestDate)

            // When
            val result = requestInformation.toRequestInformationResponse()

            // Then
            assertThat(result.requestDate).isEqualTo(requestDate)
        }

        @Test
        fun `toDomainRequestInformation should convert request to domain model`() {
            // Given
            val requestDate = LocalDate.of(2024, 3, 15)
            val request = UpdateRequestInformationRequest().apply {
                this.requestDate = requestDate
            }

            // When
            val result = request.toDomainRequestInformation()

            // Then
            assertThat(result.requestDate).isEqualTo(requestDate)
        }
    }

    @Nested
    inner class AddressNullableMapperTest {

        @Test
        fun `toAddressNullable should map all fields correctly`() {
            // Given
            val domainAddress = AddressNullable(
                street = "Main Street",
                houseNumber = "42",
                boxNumber = "A",
                zipCode = "1000",
                city = "Brussels",
                country = "Belgium",
                valueDate = LocalDate.of(2022, 1, 1),
            )

            // When
            val result = domainAddress.toAddressNullable()

            // Then
            assertThat(result.street).isEqualTo("Main Street")
            assertThat(result.houseNumber).isEqualTo("42")
            assertThat(result.boxNumber).isEqualTo("A")
            assertThat(result.zipCode).isEqualTo("1000")
            assertThat(result.city).isEqualTo("Brussels")
            assertThat(result.country).isEqualTo("Belgium")
        }

        @Test
        fun `toAddressNullable should handle null values correctly`() {
            // Given
            val domainAddress = AddressNullable(
                street = "Main Street",
                houseNumber = "42",
                boxNumber = null,
                zipCode = "1000",
                city = null,
                country = null,
                valueDate = LocalDate.of(2022, 1, 1),
            )

            // When
            val result = domainAddress.toAddressNullable()

            // Then
            assertThat(result.street).isEqualTo("Main Street")
            assertThat(result.houseNumber).isEqualTo("42")
            assertThat(result.boxNumber).isNull()
            assertThat(result.zipCode).isEqualTo("1000")
            assertThat(result.city).isNull()
            assertThat(result.country).isNull()
        }
    }

    @Nested
    inner class UnionContributionFieldsMapperTest {

        @Test
        fun `toUnionContributionFields should map all fields correctly`() {
            // Given
            val unionContribution = UnionContribution(
                authorized = true,
                effectiveDate = LocalDate.of(2024, 5, 15)
            )

            // When
            val result = unionContribution.toUnionContributionFields()

            // Then
            assertThat(result.authorized).isEqualTo(true)
            assertThat(result.effectiveDate).isEqualTo(LocalDate.of(2024, 5, 15))
        }
    }

    @Nested
    inner class HistoricalCitizenOnemMapperTest {

        @Test
        fun `toHistoricalCitizenOnemResponse should map all fields correctly`() {
            // Given
            val addressNullable = AddressNullable(
                street = "Test Street",
                houseNumber = "123",
                boxNumber = "A",
                city = "Brussels",
                zipCode = "1000",
                country = "Belgium",
                valueDate = LocalDate.of(2023, 5, 15)
            )

            val historicalCitizenOnem = HistoricalCitizenOnem(
                firstName = "John",
                lastName = "Doe",
                iban = "****************",
                bic = "GEBABEBB",
                otherPersonName = "Jane Doe",
                birthDate = LocalDate.of(1980, 1, 1),
                nationality = "Belgian",
                address = addressNullable,
                bankAccountValueDate = LocalDate.of(2023, 6, 10),
                authorized = true,
                effectiveDate = LocalDate.of(2023, 7, 20),
                paymentMode = 1,
                numbox = 12345,
            )

            // When
            val result = historicalCitizenOnem.toHistoricalCitizenOnemResponse()

            // Then
            assertThat(result.firstName).isEqualTo("John")
            assertThat(result.lastName).isEqualTo("Doe")
            assertThat(result.iban).isEqualTo("****************")
            assertThat(result.bic).isEqualTo("GEBABEBB")
            assertThat(result.otherPersonName).isEqualTo("Jane Doe")
            assertThat(result.birthDate).isEqualTo(LocalDate.of(1980, 1, 1))
            assertThat(result.nationality).isEqualTo("Belgian")
            assertThat(result.bankAccountValueDate).isEqualTo(LocalDate.of(2023, 6, 10))
            assertThat(result.addressValueDate).isEqualTo(LocalDate.of(2023, 5, 15))
            assertThat(result.unionContributionValueDate).isEqualTo(LocalDate.of(2023, 7, 20))

            // Verify address mapping
            assertThat(result.address.street).isEqualTo("Test Street")
            assertThat(result.address.houseNumber).isEqualTo("123")
            assertThat(result.address.boxNumber).isEqualTo("A")
            assertThat(result.address.city).isEqualTo("Brussels")
            assertThat(result.address.zipCode).isEqualTo("1000")
            assertThat(result.address.country).isEqualTo("Belgium")

            // Verify union due mapping
            assertThat(result.unionDue.authorized).isEqualTo(true)
            assertThat(result.unionDue.effectiveDate).isEqualTo(LocalDate.of(2023, 7, 20))
        }
    }

    @Nested
    inner class HistoricalCitizenAuthenticSourcesMapperTest {

        @Test
        fun `toHistoricalCitizenAuthenticSourcesResponse should map all fields correctly`() {
            // Given
            val addressNullable = AddressNullable(
                street = "Main Street",
                houseNumber = "42",
                boxNumber = "B",
                city = "Antwerp",
                zipCode = "2000",
                country = "Belgium",
                valueDate = LocalDate.of(2023, 4, 15)
            )

            val historicalCitizenAuthenticSources = HistoricalCitizenAuthenticSources(
                firstName = "Alice",
                lastName = "Smith",
                birthDate = LocalDate.of(1985, 3, 12),
                nationality = "Belgian",
                address = addressNullable
            )

            // When
            val result = historicalCitizenAuthenticSources.toHistoricalCitizenAuthenticSourcesResponse()

            // Then
            assertThat(result.firstName).isEqualTo("Alice")
            assertThat(result.lastName).isEqualTo("Smith")
            assertThat(result.birthDate).isEqualTo(LocalDate.of(1985, 3, 12))
            assertThat(result.nationality).isEqualTo("Belgian")
            assertThat(result.valueDate).isEqualTo(LocalDate.of(2023, 4, 15))

            // Verify address mapping
            assertThat(result.address.street).isEqualTo("Main Street")
            assertThat(result.address.houseNumber).isEqualTo("42")
            assertThat(result.address.boxNumber).isEqualTo("B")
            assertThat(result.address.city).isEqualTo("Antwerp")
            assertThat(result.address.zipCode).isEqualTo("2000")
            assertThat(result.address.country).isEqualTo("Belgium")
        }
    }

    @Nested
    inner class HistoricalCitizenC1MapperTest {

        @Test
        fun `toHistoricalCitizenC1Response should map all fields correctly`() {
            // Given
            val addressNullable = AddressNullable(
                street = "Avenue Louise",
                houseNumber = "75",
                boxNumber = "C",
                city = "Brussels",
                zipCode = "1050",
                country = "Belgium",
                valueDate = LocalDate.of(2023, 8, 20)
            )

            val historicalCitizenC1 = HistoricalCitizenC1(
                firstName = "Robert",
                lastName = "Johnson",
                iban = "****************",
                bic = "GEBABEBB",
                otherPersonName = "Sarah Johnson",
                birthDate = LocalDate.of(1975, 6, 15),
                nationality = "Belgian",
                address = addressNullable,
                bankAccountValueDate = LocalDate.of(2023, 9, 10),
                authorized = true,
                effectiveDate = LocalDate.of(2023, 10, 1),
                paymentMode = 2,
                numbox = 54321
            )

            // When
            val result = historicalCitizenC1.toHistoricalCitizenC1Response()

            // Then
            assertThat(result.firstName).isEqualTo("Robert")
            assertThat(result.lastName).isEqualTo("Johnson")
            assertThat(result.iban).isEqualTo("****************")
            assertThat(result.bic).isEqualTo("GEBABEBB")
            assertThat(result.otherPersonName).isEqualTo("Sarah Johnson")
            assertThat(result.birthDate).isEqualTo(LocalDate.of(1975, 6, 15))
            assertThat(result.nationality).isEqualTo("Belgian")
            assertThat(result.bankAccountValueDate).isEqualTo(LocalDate.of(2023, 9, 10))
            assertThat(result.addressValueDate).isEqualTo(LocalDate.of(2023, 8, 20))
            assertThat(result.unionContributionValueDate).isEqualTo(LocalDate.of(2023, 10, 1))

            // Verify address mapping
            assertThat(result.address.street).isEqualTo("Avenue Louise")
            assertThat(result.address.houseNumber).isEqualTo("75")
            assertThat(result.address.boxNumber).isEqualTo("C")
            assertThat(result.address.city).isEqualTo("Brussels")
            assertThat(result.address.zipCode).isEqualTo("1050")
            assertThat(result.address.country).isEqualTo("Belgium")

            // Verify union due mapping
            assertThat(result.unionDue.authorized).isEqualTo(true)
            assertThat(result.unionDue.effectiveDate).isEqualTo(LocalDate.of(2023, 10, 1))
        }
    }
}
