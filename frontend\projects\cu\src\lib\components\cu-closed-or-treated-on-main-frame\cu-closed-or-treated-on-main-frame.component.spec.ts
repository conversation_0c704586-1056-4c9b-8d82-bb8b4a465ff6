import { ComponentFixture, TestBed } from '@angular/core/testing';
import {RequestBasicInfoResponse} from "@rest-client/cu-bff";
import { CuClosedOrTreatedOnMainFrameComponent } from './cu-closed-or-treated-on-main-frame.component';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { OnemrvaThemeModule } from '@onemrvapublic/design-system-theme';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('CuClosedOrTreatedOnMainFrameComponent', () => {
  let component: CuClosedOrTreatedOnMainFrameComponent;
  let fixture: ComponentFixture<CuClosedOrTreatedOnMainFrameComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CuClosedOrTreatedOnMainFrameComponent,
        MatIconModule,
        TranslateModule.forRoot(),
        OnemrvaThemeModule
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(CuClosedOrTreatedOnMainFrameComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  describe('isTreatedOnMainFrame', () => {
    it('should return true when status is CLOSED and decisionType exists', () => {
      component.status = 'CLOSED';
      component.decisionType = 'TYPE1';

      expect(component.isTreatedOnMainFrame()).toBe(true);
    });

    it('should return false when status is not CLOSED', () => {
      component.status = 'OPEN';
      component.decisionType = 'TYPE1';

      expect(component.isTreatedOnMainFrame()).toBe(false);
    });

    it('should return false when decisionType is undefined', () => {
      component.status = 'CLOSED';
      component.decisionType = undefined;

      expect(component.isTreatedOnMainFrame()).toBe(false);
    });

    it('should return false when decisionType is empty string', () => {
      component.status = 'CLOSED';
      component.decisionType = '';

      expect(component.isTreatedOnMainFrame()).toBe(false);
    });
  });

  describe('isClosedAndNotTreated', () => {
    it('should return true when status is CLOSED, not treated on main frame, and next task info exists', () => {
      component.status = 'CLOSED';
      component.decisionType = '';
      component.nextTaskDescription = 'Next task';
      component.nextTaskAction = 'Action';

      expect(component.isClosedAndNotTreated()).toBe(true);
    });

    it('should return false when status is not CLOSED', () => {
      component.status = 'OPEN';
      component.decisionType = '';
      component.nextTaskDescription = 'Next task';
      component.nextTaskAction = 'Action';

      expect(component.isClosedAndNotTreated()).toBe(false);
    });

    it('should return false when treated on main frame', () => {
      component.status = 'CLOSED';
      component.decisionType = 'TYPE1';
      component.nextTaskDescription = 'Next task';
      component.nextTaskAction = 'Action';

      expect(component.isClosedAndNotTreated()).toBe(false);
    });

    it('should return false when nextTaskDescription is empty', () => {
      component.status = 'CLOSED';
      component.decisionType = '';
      component.nextTaskDescription = '';
      component.nextTaskAction = 'Action';

      expect(component.isClosedAndNotTreated()).toBe(false);
    });

    it('should return false when nextTaskAction is empty', () => {
      component.status = 'CLOSED';
      component.decisionType = '';
      component.nextTaskDescription = 'Next task';
      component.nextTaskAction = '';

      expect(component.isClosedAndNotTreated()).toBe(false);
    });

    it('should return false when both next task fields are empty', () => {
      component.status = 'CLOSED';
      component.decisionType = '';
      component.nextTaskDescription = '';
      component.nextTaskAction = '';

      expect(component.isClosedAndNotTreated()).toBe(false);
    });
  });

  describe('Input properties', () => {
    it('should properly set all input properties', () => {
      const testData = {
        status: 'CLOSED',
        decisionType: 'TYPE1',
        decisionBarema: 'BAREMA1',
        nextTaskDescription: 'Description',
        nextTaskAction: 'Action'
      };

      component.status = testData.status;
      component.decisionType = testData.decisionType;
      component.decisionBarema = testData.decisionBarema;
      component.nextTaskDescription = testData.nextTaskDescription;
      component.nextTaskAction = testData.nextTaskAction;
      fixture.detectChanges();

      expect(component.status).toBe(testData.status);
      expect(component.decisionType).toBe(testData.decisionType);
      expect(component.decisionBarema).toBe(testData.decisionBarema);
      expect(component.nextTaskDescription).toBe(testData.nextTaskDescription);
      expect(component.nextTaskAction).toBe(testData.nextTaskAction);
    });

    it('should properly set pushbackStatus input', () => {
      const testStatus = RequestBasicInfoResponse.PushbackStatusEnum.Ok;
      component.pushbackStatus = testStatus;
      fixture.detectChanges();

      expect(component.pushbackStatus).toBe(testStatus);
    });
  });

  describe('isNotClosed', () => {
    it('should return true when status is not CLOSED', () => {
      component.status = 'OPEN';
      expect(component.isNotClosed()).toBe(true);
    });

    it('should return true when status is PENDING', () => {
      component.status = 'PENDING';
      expect(component.isNotClosed()).toBe(true);
    });

    it('should return false when status is CLOSED', () => {
      component.status = 'CLOSED';
      expect(component.isNotClosed()).toBe(false);
    });

    it('should return true when status is undefined', () => {
      component.status = undefined;
      expect(component.isNotClosed()).toBe(true);
    });
  });

  describe('showMainFrameMessage', () => {
    it('should return true when status is not CLOSED', () => {
      component.status = 'OPEN';
      expect(component.showMainFrameMessage()).toBe(true);
    });

    it('should return false when status is CLOSED', () => {
      component.status = 'CLOSED';
      expect(component.showMainFrameMessage()).toBe(false);
    });
  });

  describe('Mainframe response rendering', () => {
    it('should show OK message when pushbackStatus is OK and not closed', () => {
      component.status = 'OPEN';
      component.pushbackStatus = RequestBasicInfoResponse.PushbackStatusEnum.Ok;
      fixture.detectChanges();

      const compiled = fixture.nativeElement;
      const okMessage = compiled.querySelector('.status-ok');
      expect(okMessage).toBeTruthy();
      expect(okMessage.textContent).toContain('MAINFRAME_RESPONSE.OK');
    });

    it('should show NOK message when pushbackStatus is NOK and not closed', () => {
      component.status = 'OPEN';
      component.pushbackStatus = RequestBasicInfoResponse.PushbackStatusEnum.Nok;
      fixture.detectChanges();

      const compiled = fixture.nativeElement;
      const nokMessage = compiled.querySelector('.status-nok');
      expect(nokMessage).toBeTruthy();
      expect(nokMessage.textContent).toContain('MAINFRAME_RESPONSE.NOK');
    });

    it('should show PENDING message when pushbackStatus is PENDING and not closed', () => {
      component.status = 'OPEN';
      component.pushbackStatus = RequestBasicInfoResponse.PushbackStatusEnum.Pending;
      fixture.detectChanges();

      const compiled = fixture.nativeElement;
      const pendingMessage = compiled.querySelector('.status-pending');
      expect(pendingMessage).toBeTruthy();
      expect(pendingMessage.textContent).toContain('MAINFRAME_RESPONSE.PENDING');
    });

    it('should not show mainframe messages when status is CLOSED', () => {
      component.status = 'CLOSED';
      component.pushbackStatus = RequestBasicInfoResponse.PushbackStatusEnum.Ok;
      fixture.detectChanges();

      const compiled = fixture.nativeElement;
      expect(compiled.querySelector('.status-ok')).toBeFalsy();
      expect(compiled.querySelector('.status-nok')).toBeFalsy();
      expect(compiled.querySelector('.status-pending')).toBeFalsy();
    });

    it('should handle undefined pushbackStatus gracefully', () => {
      component.status = 'OPEN';
      component.pushbackStatus = undefined;
      fixture.detectChanges();

      const compiled = fixture.nativeElement;
      expect(compiled.querySelector('.status-ok')).toBeFalsy();
      expect(compiled.querySelector('.status-nok')).toBeFalsy();
      expect(compiled.querySelector('.status-pending')).toBeFalsy();
    });
  });
});