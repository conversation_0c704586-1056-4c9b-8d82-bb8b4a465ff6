package be.fgov.onerva.cu.backend.adapter.out.external.wave

import java.util.UUID
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.adapter.out.mapper.toDomainTaskStatus
import be.fgov.onerva.cu.backend.application.domain.CreateChangePersonalDataTaskCommand
import be.fgov.onerva.cu.backend.application.domain.UpdateChangePersonalDataDecisionCommand
import be.fgov.onerva.cu.backend.application.domain.WaveTask
import be.fgov.onerva.cu.backend.application.exception.WaveException
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPort
import be.fgov.onerva.cu.backend.wo.dto.WoMetadataDTO
import be.fgov.onerva.cu.backend.wo.service.WoFacadeService
import be.fgov.onerva.cu.common.aop.LogMethodCall
import be.fgov.onerva.cu.common.aop.SensitiveParam
import be.fgov.onerva.cu.common.utils.DateUtils.formatToYYYYMMDD
import be.fgov.onerva.cu.common.utils.logger

@Service("waveAdapter")
class WaveAdapter(val woFacadeService: WoFacadeService) : WaveTaskPort {
    private val log = logger
    val DEFAULT_RVAONEM_ASSIGNEE: String = "RVA-ONEM"
    val REASON_WAITING_FOR_DECISION: String = "Waiting for decision"

    @LogMethodCall
    override fun createChangePersonalDataTask(
        requestId: UUID,
        @SensitiveParam createChangePersonalDataTaskCommand: CreateChangePersonalDataTaskCommand,
    ): WaveTask {
        log.info("Creating task for requestID/c9id: $requestId/${createChangePersonalDataTaskCommand.c9id}")
        try {
            val assignee = calculateAssignee(createChangePersonalDataTaskCommand)
            val woTaskDTO = woFacadeService.createTask(
                null,
                WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE,
                createChangePersonalDataTaskCommand.numbox,
                assignee,
                listOf(
                    WoMetadataDTO(
                        "CU_REQUEST_ID",
                        requestId.toString()
                    ),
                    WoMetadataDTO(
                        "CU_C9_TYPE",
                        createChangePersonalDataTaskCommand.type
                    ),
                    WoMetadataDTO(
                        "CU_RECEPTION_DATE",
                        createChangePersonalDataTaskCommand.receptionDate.formatToYYYYMMDD()
                    ),
                    WoMetadataDTO(
                        "CU_REQUEST_DATE",
                        createChangePersonalDataTaskCommand.requestDate.formatToYYYYMMDD()
                    ),
                    WoMetadataDTO(
                        "CU_DOSSIER_ID",
                        createChangePersonalDataTaskCommand.dossierId
                    ),
                    WoMetadataDTO(
                        "CU_ENTITY",
                        createChangePersonalDataTaskCommand.entityCode
                    ),
                    WoMetadataDTO(
                        "CU_PAYMENT_INSTITUTION",
                        createChangePersonalDataTaskCommand.sectOp
                    ),
                    WoMetadataDTO(
                        "CU_DECISION_BAREMA",
                        ""
                    ),
                    WoMetadataDTO(
                        "CU_DECISION_TYPE",
                        ""
                    )
                ),
                listOf(
                    WoMetadataDTO(
                        "CU_REQUEST_ID",
                        requestId.toString()
                    ),
                    WoMetadataDTO(
                        "CU_RECEPTION_DATE",
                        createChangePersonalDataTaskCommand.receptionDate.formatToYYYYMMDD()
                    ),
                    WoMetadataDTO(
                        "CU_ENTITY",
                        createChangePersonalDataTaskCommand.entityCode
                    ),
                    WoMetadataDTO(
                        "CU_C9_TYPE",
                        createChangePersonalDataTaskCommand.type
                    )
                )
            )
            val status = woTaskDTO.toDomainTaskStatus()
            log.info("Created task CHANGE_PERSONAL_DATA_CAPTURE ${woTaskDTO.processId}/${woTaskDTO.taskId} for requestID/c9id: $requestId/${createChangePersonalDataTaskCommand.c9id} ${assignee}")
            return WaveTask(
                processId = woTaskDTO.processId,
                taskId = woTaskDTO.taskId,
                status = status
            )
        } catch (e: Exception) {
            log.error("Error creating task for c9id: ${createChangePersonalDataTaskCommand.c9id}", e)
            throw WaveException("Error creating task for c9id: ${createChangePersonalDataTaskCommand.c9id}", e)
        }
    }

    @LogMethodCall
    override fun closeTask(taskId: String): Boolean {
        val taskIdAsLong = taskId.toLong()
        val okToClose = woFacadeService.checkTaskCanBeUpdated(taskIdAsLong)
        if (okToClose) {
            woFacadeService.closeTask(taskIdAsLong)
        }
        return okToClose
    }

    @LogMethodCall
    override fun sleepTask(taskId: String): Boolean {
        val taskIdAsLong = taskId.toLong()
        val okToClose = woFacadeService.checkTaskCanBeUpdated(taskIdAsLong)
        if (okToClose) {
            woFacadeService.sleepTask(taskIdAsLong, REASON_WAITING_FOR_DECISION)
        }
        return okToClose
    }

    @LogMethodCall
    override fun createChangePersonalDataValidateTask(
        requestId: UUID,
        processId: String,
        assignee: String,
        createChangePersonalDataTaskCommand: CreateChangePersonalDataTaskCommand,
    ): WaveTask {
        log.info("Creating task ChangePersonalDataValidateTask for requestID/c9id: $requestId/${createChangePersonalDataTaskCommand.c9id}")

        try {
            val woTaskDTO = woFacadeService.createTask(
                processId.toLong(),
                WaveTaskPort.VALIDATION_DATA,
                createChangePersonalDataTaskCommand.numbox,
                assignee,
                emptyList(),
                listOf(
                    WoMetadataDTO(
                        "CU_REQUEST_ID",
                        requestId.toString()
                    ),
                    WoMetadataDTO(
                        "CU_RECEPTION_DATE",
                        createChangePersonalDataTaskCommand.receptionDate.formatToYYYYMMDD()
                    ),
                    WoMetadataDTO(
                        "CU_ENTITY",
                        createChangePersonalDataTaskCommand.entityCode
                    ),
                    WoMetadataDTO(
                        "CU_C9_TYPE",
                        createChangePersonalDataTaskCommand.type
                    )
                )
            )
            val status = woTaskDTO.toDomainTaskStatus()
            log.info("Created task VALIDATION_DATA ${woTaskDTO.processId}/${woTaskDTO.taskId} for requestID/c9id: $requestId/${createChangePersonalDataTaskCommand.c9id} ${assignee}")
            return WaveTask(
                processId = woTaskDTO.processId,
                taskId = woTaskDTO.taskId,
                status = status
            )
        } catch (e: Exception) {
            log.error("Error creating task for c9id: ${createChangePersonalDataTaskCommand.c9id}", e)
            throw WaveException("Error creating task for c9id: ${createChangePersonalDataTaskCommand.c9id}", e)
        }
    }

    private fun calculateAssignee(createChangePersonalDataTaskComamnd: CreateChangePersonalDataTaskCommand): String =
        if (createChangePersonalDataTaskComamnd.entityCode != null) "E${createChangePersonalDataTaskComamnd.entityCode}" else DEFAULT_RVAONEM_ASSIGNEE

    override fun assignTaskToUser(taskId: String, user: String) {
        if (woFacadeService.checkTaskCanBeUpdated(taskId.toLong())) {
            woFacadeService.assignTaskToUser(taskId.toLong(), user)
        } else {
            log.warn("Task with id {} could not be assigned to user {}", taskId, user)
        }
    }

    override fun updateChangePersonalDataTaskDecision(
        processId: String,
        taskId: String,
        updateChangePersonalDataTaskDecisionCommand: UpdateChangePersonalDataDecisionCommand,
    ) {
        woFacadeService.patchProcessData(
            processId.toLong(),
            taskId.toLong(),
            listOf(
                WoMetadataDTO(
                    "CU_DECISION_TYPE",
                    updateChangePersonalDataTaskDecisionCommand.decisionType.toString(),
                ),
                WoMetadataDTO(
                    "CU_DECISION_BAREMA",
                    updateChangePersonalDataTaskDecisionCommand.decisionBarema ?: "N/A"
                ),
            )
        )
    }

    override fun closeProcess(processId: String) {
        woFacadeService.closeProcess(processId.toLong())
    }
}