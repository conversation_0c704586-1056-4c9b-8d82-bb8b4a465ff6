Feature: Change Personal Data Flow
  As a system user
  I want to submit and process change personal data requests
  So that citizen information can be updated correctly through the complete workflow

  Background:
    Given the test environment is ready

  @smoke @regression
  Scenario: Submit and process a complete change personal data request
    # Expected: C9 message triggers backend processing and creates initial task
    # Actual: Backend receives message, processes it, and creates CHANGE_PERSONAL_DATA_CAPTURE task
    Given a new change personal data request is submitted via RabbitMQ
    When I wait for the request to be processed by the backend
    Then a "CHANGE_PERSONAL_DATA_CAPTURE" task should be created for the request

    # Expected: BFF API returns aggregate data with citizen information and task is in OPEN status
    # Actual: API response contains citizen data and task status is OPEN for processing
    When I retrieve the aggregate request data for "CHANGE_PERSONAL_DATA_CAPTURE"
    Then the response should contain the citizen information
    And the task status should be "OPEN"

    # Expected: Task closure triggers creation of next task in workflow
    # Actual: CHANGE_PERSONAL_DATA_CAPTURE task is completed and VALIDATION_DATA task is created
    When I close the "CHANGE_PERSONAL_DATA_CAPTURE" task with valid data
    Then the task should be completed successfully
    And a "VALIDATION_DATA" task should be created

  @regression
  Scenario: Validate data after personal data capture
    # Expected: Completed task allows access to validation workflow
    # Actual: System provides access to validation data and applies business rules
    Given a completed "CHANGE_PERSONAL_DATA_CAPTURE" task
    When I retrieve the aggregate request data for "VALIDATION_DATA"
    Then the response should contain the updated citizen information
    And the validation rules should be applied

    # Expected: Final task closure completes the entire request workflow
    # Actual: Request is marked as completed and workflow ends successfully
    When I close the "VALIDATION_DATA" task
    Then the request should be marked as completed