package be.fgov.onerva.cu.backend.config;

import be.fgov.onerva.wo.facade.api.FacadeControllerApi;
import be.fgov.onerva.wo.organizational.chart.api.NodeApi;
import be.fgov.onerva.wo_thirdparty.api.DefaultApi;
import be.fgov.onerva.wo_thirdparty.api.PartiesApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@ConditionalOnProperty(prefix = "werkomgeving", name = "enabled", havingValue = "true")
@Configuration
public class WOConfig {

    @Bean
    public NodeApi nodeApi(RestTemplate restTemplate,
                           @Value("${woOrganizationalChartApi.url}") String woOrganizationalChartBasePath) {
        be.fgov.onerva.wo.organizational.chart.invoker.ApiClient apiClient =
                new be.fgov.onerva.wo.organizational.chart.invoker.ApiClient(restTemplate);
        apiClient.setBasePath(woOrganizationalChartBasePath);
        return new NodeApi(apiClient);
    }

    @Bean
    public PartiesApi partiesApi(RestTemplate restTemplate,
                                 @Value("${woThirdPartyApi.url}") String woThirdPartyBasePath) {
        be.fgov.onerva.wo_thirdparty.invoker.ApiClient apiClient =
                new be.fgov.onerva.wo_thirdparty.invoker.ApiClient(restTemplate);
        apiClient.setBasePath(woThirdPartyBasePath);
        return new PartiesApi(apiClient);
    }

    @Bean
    public DefaultApi partyApi(RestTemplate restTemplate,
                               @Value("${woThirdPartyApi.url}") String woThirdPartyBasePath) {
        be.fgov.onerva.wo_thirdparty.invoker.ApiClient apiClient =
                new be.fgov.onerva.wo_thirdparty.invoker.ApiClient(restTemplate);
        apiClient.setBasePath(woThirdPartyBasePath);
        return new DefaultApi(apiClient);
    }

    @Bean
    public FacadeControllerApi woFacadeApi(@Value("${werkomgeving.woFacadeApi.url}") String woFacadeBasePath) {
        return new FacadeControllerApi(woFacadeBasePath);
    }
}
