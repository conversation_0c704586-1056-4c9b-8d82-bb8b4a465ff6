import {FormUtilsService} from './form-utils.service';
import {FormControl, Validators} from '@angular/forms';

describe('FormUtilsService', () => {
  let formUtilsService: FormUtilsService;

  beforeEach(() => {
    formUtilsService = new FormUtilsService();
  });

  it('should be created', () => {
    expect(formUtilsService).toBeTruthy();
  });

  describe('checkLengthOfInput', () => {
    it('should return null if FormControl has no maxlength error', () => {
      const ctrl = new FormControl('', [Validators.required]);
      expect(FormUtilsService.checkLengthOfInput(ctrl)).toBeNull();
    });

    it('should return maxlength error details if present', () => {
      const ctrl = new FormControl('toolongvalue', [Validators.maxLength(5)]);
      const expectedError = { requiredLength: 5, actualLength: 12 };
      expect(FormUtilsService.checkLengthOfInput(ctrl)).toEqual(expectedError);
    });
  });

  describe('isClosedOrWaiting', () => {
    it('should return true when status is CLOSED', () => {
      expect(FormUtilsService.isClosedOrWaiting('CLOSED', {})).toBe(true);
    });

    it('should return true when task state code is Wait', () => {
      const task = { state: { code: 'Wait' } };
      expect(FormUtilsService.isClosedOrWaiting('OPEN', task)).toBe(true);
    });

    it('should return false when status is not CLOSED and task state code is not Wait', () => {
      const task = { state: { code: 'InProgress' } };
      expect(FormUtilsService.isClosedOrWaiting('OPEN', task)).toBe(false);
    });

    it('should return false when status is not CLOSED and task is null', () => {
      expect(FormUtilsService.isClosedOrWaiting('OPEN', null)).toBe(false);
    });

    it('should return false when status is not CLOSED and task state is undefined', () => {
      const task = {};
      expect(FormUtilsService.isClosedOrWaiting('OPEN', task)).toBe(false);
    });
  });
});
