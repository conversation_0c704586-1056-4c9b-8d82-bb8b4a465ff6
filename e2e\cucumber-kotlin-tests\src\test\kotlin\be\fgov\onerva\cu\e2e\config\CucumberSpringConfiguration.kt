package be.fgov.onerva.cu.e2e.config

import be.fgov.onerva.cu.e2e.E2ETestApplication
import io.cucumber.spring.CucumberContextConfiguration
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

/**
 * Cucumber Spring configuration for E2E tests
 *
 * This class provides the Spring Boot context configuration for Cucumber tests. It enables Spring
 * Boot test features and activates the CI profile.
 */
@CucumberContextConfiguration
@SpringBootTest(classes = [E2ETestApplication::class])
@ActiveProfiles("ci")
class CucumberSpringConfiguration
