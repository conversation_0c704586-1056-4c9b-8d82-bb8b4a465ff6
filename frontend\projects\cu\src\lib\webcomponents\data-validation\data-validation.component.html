<div class="onemrva-theme">
    <lib-loading-component></lib-loading-component>

    <lib-cu-closed-or-treated-on-main-frame *ngIf="dataConsistencyData?.basicInfo"
                                            [status]="status"
                                            [decisionType]="dataConsistencyData.basicInfo?.decisionType"
                                            [decisionBarema]="dataConsistencyData.basicInfo?.decisionBarema"
                                            [pushbackStatus]="dataConsistencyData?.basicInfo?.pushbackStatus">
    </lib-cu-closed-or-treated-on-main-frame>

    <lib-cu-c9-annexes [language]="language" [annexes]="citizenData()?.annexes">
    </lib-cu-c9-annexes>

    <lib-data-consistency
            [language]="language"
            [taskStatus]="status"
            [task]="task"
            [dataConsistencyData]="dataConsistencyData"
            (fieldSourcesChange)="onFieldSourcesChange($event)"
            (regisVerificationEvent)="handleRegisVerificationChange($event)"
            (tableConsistencyChanged)="onTableConsistencyChanged($event)"
    ></lib-data-consistency>
    <div class="actions">
        <button mat-button
                id="saveAsDraftButton"
                [hidden]="isFormClosedOrWaiting()"
                (click)="save()"
                color="primary"
                aria-label="Basic"
                data-cy="saveAsDraftRequestButton"
        >{{ 'CU_DATA_CONSISTENCY.BUTTONS.SAVE_AS_DRAFT' | translate }}
        </button>

        <button mat-stroked-button
                id="sendC51Button"
                [hidden]="isFormClosedOrWaiting()"
                (click)="sendC51()"
                color="primary"
                aria-label="Basic"
                data-cy="sendC51RequestButton"
        >{{ 'CU_DATA_CONSISTENCY.BUTTONS.SEND_C51' | translate }}
        </button>

        <button mat-flat-button
                id="validateButton"
                [hidden]="isFormClosedOrWaiting()"
                [disabled]="!areActionButtonsEnabled || !isTableConsistent"
                (click)="validateAndContinue()"
                color="accent"
                aria-label="Basic"
                data-cy="validateAndContinueButton"
        >{{ 'CU_DATA_CONSISTENCY.BUTTONS.VALIDATE' | translate }}
        </button>


        <div class="S24Action">
            <button mat-stroked-button
                    id="openS24Session"
                    [hidden]="isFormClosedOrWaiting()"
                    (click)="openS24Session()"
                    color="primary"
                    aria-label="Basic"
                    data-cy="openS24SessionButton"
            >{{ 'CU_DATA_CONSISTENCY.BUTTONS.OPEN_S24' | translate }}
            </button>
            <span class="tooltip-container" *ngIf="!isFormClosedOrWaiting()">
            <mat-icon class="filled" color="info" matTooltipPosition="right"
                      [matTooltip]="'CU_DATA_CONSISTENCY.S24_DIALOG.TOOLTIP' | translate"
                      aria-label="Tooltip icon">
                info
            </mat-icon>
        </span></div>

    </div>
</div>
