Feature: Error Handling and Negative Test Cases
  As a system administrator
  I want to ensure the system handles errors gracefully
  So that failures are managed properly and don't cause system instability

  Background:
    Given the test environment is ready

  @regression @error-handling
  Scenario: Handle RabbitMQ message publishing failures
    # Expected: System gracefully handles RabbitMQ connection issues
    # Actual: <PERSON><PERSON><PERSON> is caught, logged, and appropriate exception is thrown
    Given the RabbitMQ connection is unavailable
    When I attempt to submit a change personal data request via RabbitMQ
    Then a connection error should be thrown
    And the error should be logged appropriately
    And the system should remain stable

  @regression @error-handling
  Scenario: Handle malformed RabbitMQ messages
    # Expected: System rejects malformed messages without crashing
    # Actual: Invalid message format is detected and rejected with clear error
    Given I have a malformed C9 message
    When I attempt to submit the malformed message via RabbitMQ
    Then a message format error should be thrown
    And the invalid message should be rejected
    And the error details should be logged

  @regression @error-handling
  Scenario: Handle backend processing timeouts
    # Expected: System handles backend processing delays gracefully
    # Actual: Timeout is detected and appropriate error handling is triggered
    Given a new change personal data request is submitted via RabbitMQ
    When the backend processing takes longer than expected
    And I wait for the request to be processed by the backend with timeout
    Then a timeout error should be handled gracefully
    And the system should provide appropriate feedback
    And no data corruption should occur

  @regression @error-handling
  Scenario: Handle API timeout during BFF calls
    # Expected: API timeouts are handled without system failure
    # Actual: Timeout is detected, logged, and user receives appropriate error
    Given a new change personal data request is submitted via RabbitMQ
    And I wait for the request to be processed by the backend
    And a "CHANGE_PERSONAL_DATA_CAPTURE" task should be created for the request
    When I attempt to retrieve aggregate request data with API timeout
    Then an API timeout error should be handled
    And the error should be logged with appropriate details
    And the system should remain responsive

  @regression @error-handling
  Scenario: Handle WO-Facade API unavailability
    # Expected: System handles WO-Facade service unavailability
    # Actual: Service unavailability is detected and handled appropriately
    Given the WO-Facade service is unavailable
    When I attempt to search for tasks
    Then a service unavailable error should be handled
    And the error should be logged appropriately
    And fallback behavior should be triggered if available

  @regression @error-handling
  Scenario: Handle authentication failures
    # Expected: Authentication failures are handled securely
    # Actual: Invalid credentials are rejected and security is maintained
    Given invalid authentication credentials are provided
    When I attempt to access the BFF API
    Then an authentication error should be returned
    And access should be denied
    And the security violation should be logged

  @regression @error-handling
  Scenario: Handle authorization failures
    # Expected: Insufficient permissions are handled properly
    # Actual: Unauthorized access attempts are blocked with clear messaging
    Given valid authentication but insufficient permissions
    When I attempt to access restricted BFF endpoints
    Then an authorization error should be returned
    And access should be denied
    And the authorization failure should be logged

  @regression @error-handling
  Scenario: Handle database connection failures
    # Expected: Database connectivity issues are handled gracefully
    # Actual: Connection failures are detected and appropriate errors are returned
    Given the database connection is unavailable
    When I attempt to retrieve aggregate request data
    Then a database connection error should be handled
    And the error should be logged with connection details
    And the system should attempt recovery if possible

  @regression @error-handling
  Scenario: Handle invalid task state transitions
    # Expected: Invalid task operations are prevented
    # Actual: System validates task state and prevents invalid transitions
    Given a new change personal data request is submitted via RabbitMQ
    And I wait for the request to be processed by the backend
    And a "CHANGE_PERSONAL_DATA_CAPTURE" task should be created for the request
    When I attempt to close a task that is already closed
    Then an invalid state transition error should be returned
    And the task state should remain unchanged
    And the error should be logged with task details

  @regression @error-handling
  Scenario: Handle concurrent task modifications
    # Expected: Concurrent modifications are handled safely
    # Actual: System prevents data corruption from simultaneous updates
    Given a new change personal data request is submitted via RabbitMQ
    And I wait for the request to be processed by the backend
    And a "CHANGE_PERSONAL_DATA_CAPTURE" task should be created for the request
    When multiple users attempt to modify the same task simultaneously
    Then only one modification should succeed
    And concurrent modification conflicts should be handled
    And data integrity should be maintained

  @regression @error-handling @edge-case
  Scenario: Handle extremely large message payloads
    # Expected: System handles large payloads within defined limits
    # Actual: Large messages are either processed or rejected with clear limits
    Given I have a C9 message with extremely large payload
    When I attempt to submit the large message via RabbitMQ
    Then the system should handle the large payload appropriately
    And memory usage should remain within acceptable limits
    And appropriate limits should be enforced if exceeded

  @regression @error-handling @edge-case
  Scenario: Handle special characters and encoding issues
    # Expected: System handles various character encodings properly
    # Actual: Special characters are processed correctly without corruption
    Given I have a C9 message with special characters and unicode
    When I submit the message via RabbitMQ
    And I wait for the request to be processed by the backend
    Then the special characters should be preserved correctly
    And no encoding corruption should occur
    And the data should be retrievable with original formatting

  @regression @error-handling
  Scenario: Handle system resource exhaustion
    # Expected: System handles resource constraints gracefully
    # Actual: Resource limits are monitored and appropriate actions are taken
    Given the system is under high load
    When I submit multiple change personal data requests simultaneously
    Then the system should handle the load appropriately
    And resource exhaustion should be prevented or handled gracefully
    And system stability should be maintained
