package be.fgov.onerva.cu.backend.adapter.`in`.controller

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.application.domain.Annex
import be.fgov.onerva.cu.backend.application.domain.AnnexType
import be.fgov.onerva.cu.backend.application.domain.RequestBasicInfo
import be.fgov.onerva.cu.backend.application.domain.RequestInformation
import be.fgov.onerva.cu.backend.application.domain.WaveTask
import be.fgov.onerva.cu.backend.application.domain.WaveTaskStatus
import be.fgov.onerva.cu.backend.application.port.`in`.AssignTaskToUserUseCase
import be.fgov.onerva.cu.backend.application.port.`in`.CloseRequestTaskUseCase
import be.fgov.onerva.cu.backend.application.port.`in`.RequestBasicInfoUseCase
import be.fgov.onerva.cu.backend.application.port.`in`.RequestInformationUseCase
import be.fgov.onerva.cu.rest.priv.model.UpdateRequestInformationRequest
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class RequestInformationControllerTest {
    @MockK
    lateinit var requestBasicInfoUseCase: RequestBasicInfoUseCase

    @MockK
    lateinit var closeRequestTaskUseCase: CloseRequestTaskUseCase

    @MockK
    lateinit var assignTaskToUserUseCase: AssignTaskToUserUseCase

    @MockK
    lateinit var requestInformationUseCase: RequestInformationUseCase

    @InjectMockKs
    lateinit var controller: RequestInformationController

    @Test
    fun `should return request basic info when request exists`() {
        // Given
        val requestId = UUID.fromString("123e4567-e89b-12d3-a456-************")
        val requestBasicInfo = RequestBasicInfo(
            requestDate = LocalDate.of(2024, 12, 16),
            ssin = "123456789",
            firstName = "John",
            lastName = "Doe",
            introductionDate = LocalDate.of(2024, 12, 15),
            dateValid = LocalDate.of(2024, 12, 15),
            annexes = listOf(
                Annex(
                    url = "http://the-scan-url",
                    type = AnnexType.SCANNED_DOCUMENTS
                )
            ),
            decisionType = null,
            decisionBarema = null,
            c9Id = 12345,
            pushbackStatus = null,
        )

        every { requestBasicInfoUseCase.getRequestBasicInfo(requestId) } returns requestBasicInfo

        // When
        val response = controller.getRequestBasicInfo(requestId)

        // Then
        assertThat(response)
            .isNotNull()
            .extracting("requestDate", "ssin", "firstName", "lastName", "introductionDate", "dateValid", "annexes")
            .containsExactly(
                LocalDate.of(2024, 12, 16),
                "123456789",
                "John",
                "Doe",
                LocalDate.of(2024, 12, 15),
                LocalDate.of(2024, 12, 15),
                listOf(
                    be.fgov.onerva.cu.rest.priv.model.Annex()
                        .url("http://the-scan-url")
                        .type(be.fgov.onerva.cu.rest.priv.model.AnnexType.SCANNED_DOCUMENTS),
                )
            )

        verify(exactly = 1) { requestBasicInfoUseCase.getRequestBasicInfo(requestId) }
    }

    @Test
    fun `should throw exception when requestId is null`() {
        // When/Then
        assertThatThrownBy { controller.getRequestBasicInfo(null) }
            .isInstanceOf(NullPointerException::class.java)

        verify(exactly = 0) { requestBasicInfoUseCase.getRequestBasicInfo(any()) }
    }

    @Test
    fun `should propagate exception when use case throws exception`() {
        // Given
        val requestId = UUID.fromString("123e4567-e89b-12d3-a456-************")
        val expectedException = RuntimeException("Request not found")

        every { requestBasicInfoUseCase.getRequestBasicInfo(requestId) } throws expectedException

        // When/Then
        assertThatThrownBy { controller.getRequestBasicInfo(requestId) }
            .isInstanceOf(RuntimeException::class.java)
            .hasMessage("Request not found")

        verify(exactly = 1) { requestBasicInfoUseCase.getRequestBasicInfo(requestId) }
    }

    @Test
    fun `closeRequestTask should successfully close task`() {
        // Given
        val requestId = UUID.fromString("123e4567-e89b-12d3-a456-************")
        val taskCode = "TASK-123"
        val waveTask = WaveTask(
            processId = "PROCESS-123",
            taskId = "TASK-123",
            status = WaveTaskStatus.OPEN
        )

        every { closeRequestTaskUseCase.closeTaskForRequestAndCreateNext(requestId, taskCode) } returns waveTask

        // When
        controller.closeRequestTask(requestId, taskCode)

        // Then
        verify(exactly = 1) { closeRequestTaskUseCase.closeTaskForRequestAndCreateNext(requestId, taskCode) }
    }

    @Test
    fun `closeRequestTask return null when use case returns null`() {
        // Given
        val requestId = UUID.fromString("123e4567-e89b-12d3-a456-************")
        val taskCode = "TASK-123"

        every { closeRequestTaskUseCase.closeTaskForRequestAndCreateNext(requestId, taskCode) } returns null

        // When
        val waveTaskResponse = controller.closeRequestTask(requestId, taskCode)

        // Then
        assertThat(waveTaskResponse).isNull()
        verify(exactly = 1) { closeRequestTaskUseCase.closeTaskForRequestAndCreateNext(requestId, taskCode) }
    }

    @Test
    fun `closeRequestTask should throw exception when requestId is null`() {
        // Given
        val taskCode = "TASK-123"

        // When/Then
        assertThatThrownBy { controller.closeRequestTask(null, taskCode) }
            .isInstanceOf(NullPointerException::class.java)

        verify(exactly = 0) { closeRequestTaskUseCase.closeTaskForRequestAndCreateNext(any(), any()) }
    }

    @Test
    fun `closeRequestTask should throw exception when taskCode is null`() {
        // Given
        val requestId = UUID.fromString("123e4567-e89b-12d3-a456-************")

        // When/Then
        assertThatThrownBy { controller.closeRequestTask(requestId, null) }
            .isInstanceOf(NullPointerException::class.java)

        verify(exactly = 0) { closeRequestTaskUseCase.closeTaskForRequestAndCreateNext(any(), any()) }
    }

    @Test
    fun `closeRequestTask should propagate exception when use case throws exception`() {
        // Given
        val requestId = UUID.fromString("123e4567-e89b-12d3-a456-************")
        val taskCode = "TASK-123"
        val expectedException = RuntimeException("Failed to close task")

        every { closeRequestTaskUseCase.closeTaskForRequestAndCreateNext(requestId, taskCode) } throws expectedException

        // When/Then
        assertThatThrownBy { controller.closeRequestTask(requestId, taskCode) }
            .isInstanceOf(RuntimeException::class.java)
            .hasMessage("Failed to close task")

        verify(exactly = 1) { closeRequestTaskUseCase.closeTaskForRequestAndCreateNext(requestId, taskCode) }
    }

    @Test
    fun `assignTaskToUser should successfully assign task`() {
        // Given
        val requestId = UUID.fromString("123e4567-e89b-12d3-a456-************")
        every { assignTaskToUserUseCase.assignTaskToUser(requestId) } returns Unit

        // When
        controller.assignTaskToUser(requestId)

        // Then
        verify(exactly = 1) { assignTaskToUserUseCase.assignTaskToUser(requestId) }
    }

    @Test
    fun `getRequestInformation should return request information when request exists`() {
        // Given
        val requestId = UUID.fromString("123e4567-e89b-12d3-a456-************")
        val requestInformation = RequestInformation(
            requestDate = LocalDate.of(2024, 12, 16)
        )

        every { requestInformationUseCase.getRequestInformation(requestId) } returns requestInformation

        // When
        val response = controller.getRequestInformation(requestId)

        // Then
        assertThat(response)
            .isNotNull()
            .extracting("requestDate")
            .isEqualTo(LocalDate.of(2024, 12, 16))

        verify(exactly = 1) { requestInformationUseCase.getRequestInformation(requestId) }
    }

    @Test
    fun `updateRequestInformation should call updateRequestInformation`() {
        // Given
        val requestId = UUID.randomUUID()
        val updateRequestInformationRequest = UpdateRequestInformationRequest()
            .requestDate(LocalDate.now())
        every { requestInformationUseCase.updateRequestInformation(requestId, any()) } returns Unit

        // When
        controller.updateRequestInformation(requestId, updateRequestInformationRequest)

        // Then
        verify(exactly = 1) { requestInformationUseCase.updateRequestInformation(requestId, any()) }
    }
}