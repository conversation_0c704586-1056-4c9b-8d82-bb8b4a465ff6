# Cucumber Configuration Properties
# This file configures Cucumber test execution and reporting

# Plugin configuration for test reporting
cucumber.plugin=pretty,html:target/cucumber-reports/cucumber.html,json:target/cucumber-reports/cucumber.json,junit:target/cucumber-reports/cucumber.xml

# Glue packages - where <PERSON><PERSON><PERSON><PERSON> looks for step definitions
cucumber.glue=be.fgov.onerva.cu.e2e

# Features location
cucumber.features=classpath:features

# Execution configuration
cucumber.execution.parallel.enabled=false
cucumber.execution.strict=true

# Publishing configuration
cucumber.publish.enabled=false
cucumber.publish.quiet=true

# Filter configuration
# Uncomment to run only smoke tests:
# cucumber.filter.tags=@smoke

# Uncomment to exclude error handling tests during development:
# cucumber.filter.tags=not @error-handling

# Snippet type for undefined steps
cucumber.snippet-type=camelcase

# Object factory for dependency injection
cucumber.object-factory=org.springframework.test.context.junit.jupiter.SpringJUnitConfig

# Logging configuration
cucumber.logging.level=INFO

# Report configuration
cucumber.plugin.html.output.dir=target/cucumber-reports
cucumber.plugin.json.output.dir=target/cucumber-reports
cucumber.plugin.junit.output.dir=target/cucumber-reports

# Step definition detection
cucumber.glue.scan.enabled=true

# Dry run configuration (set to true to check step definitions without execution)
cucumber.execution.dry-run=false

# Wip (Work In Progress) configuration
cucumber.execution.wip=false

# Monochrome output (removes ANSI color codes)
cucumber.plugin.pretty.monochrome=false
