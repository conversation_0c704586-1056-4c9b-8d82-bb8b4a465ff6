package be.fgov.onerva.cu.e2e

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication

/**
 * Spring Boot application for E2E tests
 * 
 * This class provides the main Spring Boot configuration for the E2E test module.
 * It enables auto-configuration and component scanning for the test environment.
 */
@SpringBootApplication
class E2ETestApplication

fun main(args: Array<String>) {
    runApplication<E2ETestApplication>(*args)
}
