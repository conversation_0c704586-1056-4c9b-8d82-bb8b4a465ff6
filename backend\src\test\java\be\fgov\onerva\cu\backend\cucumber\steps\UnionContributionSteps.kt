package be.fgov.onerva.cu.backend.cucumber.steps

import java.math.BigDecimal
import org.assertj.core.api.Assertions.assertThat
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.ResultActions
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import be.fgov.onerva.cu.backend.integration.helpers.JdbcHelper
import be.fgov.onerva.cu.rest.priv.model.ExternalSource
import be.fgov.onerva.cu.rest.priv.model.FieldSource
import be.fgov.onerva.cu.rest.priv.model.SelectFieldSourcesRequest
import be.fgov.onerva.person.api.CitizenInfoApi
import be.fgov.onerva.person.rest.model.BankAccountDTO
import be.fgov.onerva.person.rest.model.CitizenInfoDTO
import be.fgov.onerva.person.rest.model.CitizenInfoPageDTO
import com.fasterxml.jackson.databind.ObjectMapper
import io.cucumber.datatable.DataTable
import io.cucumber.java.en.Given
import io.cucumber.java.en.Then
import io.cucumber.java.en.When
import io.cucumber.spring.ScenarioScope
import io.mockk.every

@ScenarioScope
class UnionContributionSteps : BaseSteps() {
    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Autowired
    private lateinit var jdbcHelper: JdbcHelper

    @Autowired
    private lateinit var citizenInfoApi: CitizenInfoApi

    private lateinit var result: ResultActions

    @Given("a citizen with SSIN {string} exists with union information")
    fun setupCitizenInfo(ssin: String) {
        every {
            citizenInfoApi.searchCitizenInfo(
                listOf(ssin),
                null,
                "SUMMARY",
                0,
                10
            )
        } returns CitizenInfoPageDTO().apply {
            pageNumber = 0
            pageSize = 10
            totalElements = 1
            isFirst = true
            isLast = true
            content = listOf(
                CitizenInfoDTO().apply {
                    firstName = "John"
                    lastName = "Doe"
                    numBox = BigDecimal.valueOf(42)
                    flagNation = BigDecimal.valueOf(151)
                    iban = "****************"
                    address = "Main Street 123 Box A"
                    postalCode = "1000"
                    bankAccount = BankAccountDTO().apply {
                        iban = "****************"
                        bic = "CMRPL"
                    }
                }
            )
        }
    }

    @When("I select the following sources for union contribution:")
    fun selectSourcesForUnionContribution(dataTable: DataTable) {
        val requestId = requireNotNull(testContext.requestId) { "Request ID must not be null" }

        val fieldSources = dataTable.asMaps().map { row ->
            FieldSource().apply {
                fieldName = row["fieldName"]
                source = ExternalSource.valueOf(row["source"]!!)
            }
        }

        val request = SelectFieldSourcesRequest().apply {
            this.fieldSources = fieldSources
        }

        result = mockMvc.perform(
            put("/api/requests/$requestId/union-contribution/select")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
                .withAuth()
        )
    }

    @Then("the union contribution should be updated with:")
    fun verifyUnionContributionUpdated(dataTable: DataTable) {
        val requestId = requireNotNull(testContext.requestId) { "Request ID must not be null" }

        val expectedValues = dataTable.asMap()
        val unionContribution = jdbcHelper.getUnionContribution(requestId)

        expectedValues.forEach { (key, value) ->
            if (key == "effective_date") {
                // Handle date conversion
                assertThat(unionContribution).containsEntry(key, java.sql.Date.valueOf(value))
            } else if (key == "authorized") {
                // Handle boolean conversion
                assertThat(unionContribution).containsEntry(key, value.toBoolean())
            } else {
                assertThat(unionContribution).containsEntry(key, value)
            }
        }
    }

    @Then("the union contribution response status should be {int}")
    fun verifyResponseStatus(status: Int) {
        result.andExpect(status().`is`(status))
    }
}