import {Component, EventEmitter, Input, Output} from "@angular/core";
import {MatAccordion} from "@angular/material/expansion";
import {TranslatePipe} from "@ngx-translate/core";
import {
    OnemrvaMatPanelComponent,
    OnemrvaMatPanelContentComponent,
    OnemrvaMatPanelTitleComponent,
} from "@onemrvapublic/design-system/mat-panel";
import {HistoricalBaremaResponse} from "@rest-client/cu-bff";
import {RegisCheckComponent} from "./regis-check/regis-check.component";

@Component({
    selector: "lib-family-composition",
    imports: [
        MatAccordion,
        OnemrvaMatPanelComponent,
        OnemrvaMatPanelContentComponent,
        OnemrvaMatPanelTitleComponent,
        TranslatePipe,
        RegisCheckComponent,
    ],
    templateUrl: "./family-composition.component.html",
    styleUrl: "./family-composition.component.scss",
    standalone: true,
})
export class FamilyCompositionComponent {

    @Input() barema?: HistoricalBaremaResponse;
    @Input() requestId: string | undefined;
    @Input() language!: string;
    @Output() regisVerificationEvent = new EventEmitter<boolean>();

    onVerificationChange(isVerified: boolean): void {
        this.regisVerificationEvent.emit(isVerified);
    }
}
