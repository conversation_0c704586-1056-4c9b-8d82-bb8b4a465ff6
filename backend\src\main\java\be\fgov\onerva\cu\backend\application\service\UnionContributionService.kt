package be.fgov.onerva.cu.backend.application.service

import java.time.LocalDate
import java.util.UUID
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import be.fgov.onerva.cu.backend.application.domain.ExternalSource
import be.fgov.onerva.cu.backend.application.domain.FieldSource
import be.fgov.onerva.cu.backend.application.domain.UnionContribution
import be.fgov.onerva.cu.backend.application.domain.UpdateUnionContribution
import be.fgov.onerva.cu.backend.application.exception.InvalidExternalDataException
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.port.`in`.UnionContributionUseCase
import be.fgov.onerva.cu.backend.application.port.out.FieldSourcePort
import be.fgov.onerva.cu.backend.application.port.out.UnionContributionPort
import be.fgov.onerva.cu.backend.application.validation.UniqueFieldSources
import be.fgov.onerva.cu.backend.application.validation.ValidFieldNames
import be.fgov.onerva.cu.common.aop.LogMethodCall
import be.fgov.onerva.cu.common.aop.SensitiveParam

/**
 * Service implementation for managing union contribution information.
 *
 * This service implements the [UnionContributionUseCase] and handles the business logic for
 * retrieving and updating union contribution information through the persistence adapter.
 *
 * @property unionContributionPort The persistence adapter for union contribution information.
 */
@Service
@Validated
class UnionContributionService(
    private val unionContributionPort: UnionContributionPort,
    private val fieldSourcePort: FieldSourcePort,
    val historicalInformationService: HistoricalInformationService,
) : UnionContributionUseCase {

    // Entity type constant for field source tracking
    companion object {
        const val ENTITY_TYPE = "union_contribution"
    }

    @LogMethodCall
    override fun getUnionContribution(requestId: UUID): UnionContribution? =
        unionContributionPort.getUnionContribution(requestId)

    @LogMethodCall
    override fun updateUnionContribution(
        requestId: UUID,
        @Valid @SensitiveParam updateUnionContribution: UpdateUnionContribution,
    ) {
        unionContributionPort.persistUnionContribution(
            requestId, UnionContribution(
                updateUnionContribution.authorized,
                updateUnionContribution.effectiveDate,
            )
        )
    }

    /**
     * Get all field sources for a union contribution entity
     */
    @LogMethodCall
    override fun getUnionContributionFieldSources(requestId: UUID): List<FieldSource> {
        val entityId = unionContributionPort.getEntityId(requestId)
        return fieldSourcePort.getAllFieldSources(ENTITY_TYPE, entityId)
    }

    /**
     * Select field sources for union contribution fields
     */
    @LogMethodCall
    override fun selectUnionContributionFieldSources(
        requestId: UUID,
        @Valid
        @UniqueFieldSources
        @ValidFieldNames(names = ["contribution"])
        fieldSources: List<FieldSource>,
    ) {
        val modeOfPayment = getUnionContribution(requestId)
            ?: throw RequestIdNotFoundException("Mode of payment not found for request ID: $requestId")
        val c1UnionContribution = historicalInformationService.getUnionContribution(requestId, ExternalSource.C1)
//        val onemCitizenInformation = historicalInformationService.getCitizenInformation(requestId, ExternalSource.ONEM)
        var authorized: Boolean? = null
        var effectiveDate: LocalDate? = null

        fieldSources.forEach { fieldSource ->
            when (fieldSource.fieldName) {
                "contribution" -> {
                    when (fieldSource.source) {
                        ExternalSource.C1 -> {
                            authorized = c1UnionContribution.authorized
                            effectiveDate = c1UnionContribution.effectiveDate
                        }
                        ExternalSource.ONEM -> throw InvalidExternalDataException("ONEM does not have a field for ${fieldSource.fieldName}")
                        ExternalSource.AUTHENTIC_SOURCES -> throw InvalidExternalDataException("Authentic sources does not have a field for ${fieldSource.fieldName}")
                    }
                }
            }
        }

        unionContributionPort.persistUnionContribution(
            requestId, UnionContribution(
                authorized = authorized ?: modeOfPayment.authorized,
                effectiveDate = effectiveDate ?: modeOfPayment.effectiveDate,
            )
        )

        val entityId = unionContributionPort.getEntityId(requestId)
        fieldSourcePort.setMultipleFieldSources(ENTITY_TYPE, entityId, fieldSources)
    }
}