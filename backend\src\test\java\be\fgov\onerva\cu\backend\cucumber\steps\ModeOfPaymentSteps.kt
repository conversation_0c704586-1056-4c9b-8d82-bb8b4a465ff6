package be.fgov.onerva.cu.backend.cucumber.steps

import be.fgov.onerva.cu.backend.integration.helpers.JdbcHelper
import be.fgov.onerva.cu.rest.priv.model.ExternalSource
import be.fgov.onerva.cu.rest.priv.model.FieldSource
import be.fgov.onerva.cu.rest.priv.model.SelectFieldSourcesRequest
import be.fgov.onerva.cu.rest.priv.model.UpdateModeOfPaymentRequest
import be.fgov.onerva.person.api.CitizenInfoApi
import be.fgov.onerva.person.rest.model.*
import com.fasterxml.jackson.databind.ObjectMapper
import io.cucumber.datatable.DataTable
import io.cucumber.java.en.Given
import io.cucumber.java.en.Then
import io.cucumber.java.en.When
import io.cucumber.spring.ScenarioScope
import io.mockk.every
import org.assertj.core.api.Assertions.assertThat
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import java.math.BigDecimal

@ScenarioScope
class ModeOfPaymentSteps : BaseSteps() {
    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Autowired
    private lateinit var jdbcHelper: JdbcHelper

    @Autowired
    private lateinit var citizenInfoApi: CitizenInfoApi

    @Given("a citizen with SSIN {string} exists in the system")
    fun setupCitizenInfo(ssin: String) {
        every {
            citizenInfoApi.searchCitizenInfo(
                listOf(ssin),
                null,
                "SUMMARY",
                0,
                10
            )
        } returns CitizenInfoPageDTO().also {
            it.pageNumber = 0
            it.pageSize = 10
            it.totalElements = 1
            it.isFirst = true
            it.isLast = true
            it.content = listOf(
                CitizenInfoDTO().also {
                    it.firstName = "John"
                    it.lastName = "Doe"
                    it.numBox = BigDecimal.valueOf(42)
                    it.flagNation = BigDecimal.valueOf(151)
                    it.iban = "****************"
                    it.address = "Main Street 123 Box A"
                    it.postalCode = "1000"
                    it.bankAccount = BankAccountDTO().also {
                        it.iban = "****************"
                        it.bic = "CMRPL"
                    }
                    it.addressObj = ForeignAddressDTO().also {
                        it.city = "Brussels"
                        it.street = "Main Street"
                        it.box = "Box A"
                        it.countryCode = 150
                        it.zip = "1000"
                        it.number = "123"
                    }
                    it.unionDue = CitizenInfoUnionDueDTO().also {
                        it.mandateActive = false
                        it.validFrom = null
                    }
                }
            )
        }
    }

    @When("I select the following sources for mode of payment:")
    fun selectSourcesForModeOfPayment(dataTable: DataTable) {
        val requestId = requireNotNull(testContext.requestId) { "Request ID must not be null" }

        val auth = SecurityContextHolder.getContext().authentication
        println("Authentication after setting: $auth")
        println("Authorities: ${auth?.authorities}")

        val fieldSources = dataTable.asMaps().map { row ->
            FieldSource().apply {
                fieldName = row["fieldName"]
                source = ExternalSource.valueOf(row["source"]!!)
            }
        }

        val request = SelectFieldSourcesRequest().apply {
            this.fieldSources = fieldSources
        }

        testContext.result = mockMvc.perform(
            put("/api/requests/$requestId/mode-of-payment/select")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
                .withAuth()
        )
    }

    @When("I update the mode of payment with:")
    fun updateModeOfPayment(dataTable: DataTable) {
        val requestId = requireNotNull(testContext.requestId) { "Request ID must not be null" }

        val params = dataTable.asMap()

        val request = UpdateModeOfPaymentRequest().apply {
            iban = params["iban"]
            bic = params["bic"]
            otherPersonName = params["otherPersonName"]
        }

        testContext.result = mockMvc.perform(
            put("/api/requests/${requestId}/mode-of-payment")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
                .withAuth()
        )
    }

    @Then("the mode of payment should be updated with:")
    fun verifyModeOfPaymentUpdated(dataTable: DataTable) {
        val requestId = requireNotNull(testContext.requestId) { "Request ID must not be null" }

        val expectedValues = dataTable.asMap()
        val modeOfPayment = jdbcHelper.getModeOfPayment(requestId)

        expectedValues.forEach { (key, value) ->
            assertThat(modeOfPayment).containsEntry(key, value)
        }
    }

    @Then("if successful, the database should be updated")
    fun verifyDatabaseUpdatedIfSuccessful() {
        val requestId = requireNotNull(testContext.requestId) { "Request ID must not be null" }

        val status = testContext.result!!.andReturn().response.status
        if (status == 204) {
            val modeOfPayment = jdbcHelper.getModeOfPayment(requestId)
            assertThat(modeOfPayment).isNotNull
        }
    }
}