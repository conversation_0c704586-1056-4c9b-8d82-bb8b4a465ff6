package be.fgov.onerva.cu.backend.adapter.`in`.controller

import java.util.UUID
import org.springframework.context.annotation.Profile
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequestReceivedCommand
import be.fgov.onerva.cu.backend.application.port.`in`.ChangePersonalDataRequestUseCase

@RestController()
@RequestMapping("api/e2e")
@Profile("dev", "ci")
class E2ETestController(
    val changePersonalDataRequestUseCase: ChangePersonalDataRequestUseCase,
) {

    @PostMapping("/create-basic-cases")
    fun createBasicE2ECases(): List<UUID> {
        val cases = listOf(
            ChangePersonalDataRequestReceivedCommand(
                c9Id = 12345,
                type = "410",
                ssin = "18031307065",
                ec1Id = 12340
            ),
            ChangePersonalDataRequestReceivedCommand(
                c9Id = 123456,
                type = "410",
                ssin = "18031307065",
                ec1Id = null
            )
        )
        val uuids = cases.map { case ->
            changePersonalDataRequestUseCase.receivedChangePersonalData(case)!!
        }
        return uuids
    }
}