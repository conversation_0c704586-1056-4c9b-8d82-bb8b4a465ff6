package be.fgov.onerva.cu.bff.adapter.out

import java.math.BigDecimal
import java.util.stream.Stream
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import be.fgov.onerva.cu.bff.rest.client.citizen.api.CitizenInfoApi
import be.fgov.onerva.cu.bff.rest.client.citizen.model.BankAccountDTO
import be.fgov.onerva.cu.bff.rest.client.citizen.model.CitizenInfoDTO
import be.fgov.onerva.cu.bff.rest.client.citizen.model.CitizenInfoPageDTO
import be.fgov.onerva.cu.common.utils.parseCitizenAddress
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify

@ExtendWith(MockKExtension::class)
@Execution(ExecutionMode.SAME_THREAD)
class CitizenInfoServiceTest {

    @MockK
    lateinit var citizenInfoApi: CitizenInfoApi

    @MockK
    lateinit var citizenInfoApiFactory: CitizenInfoApiFactory

    @InjectMockKs
    lateinit var citizenInfoService: CitizenInfoService

    @BeforeEach
    fun setup() {
        mockkStatic(::parseCitizenAddress)
        every { citizenInfoApiFactory(any()) } returns citizenInfoApi
    }

    @AfterEach
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun `getCitizenInfo should return null when no citizen found`() {
        // Given
        val ssin = "*********"
        val emptyPageResponse = CitizenInfoPageDTO().apply {
            content = emptyList()
            pageNumber = 0
            pageSize = 10
            totalElements = 0
            isFirst = true
            isLast = true
        }

        every {
            citizenInfoApi.searchCitizenInfo(
                listOf(ssin),
                null,
                "SUMMARY",
                0,
                10
            )
        } returns emptyPageResponse

        // When
        val result = citizenInfoService.getCitizenInfo(ssin, "Bearer token")

        // Then
        assertThat(result).isNull()
        verify(exactly = 1) { citizenInfoApi.searchCitizenInfo(listOf(ssin), null, "SUMMARY", 0, 10) }
    }

    @Test
    fun `getCitizenInfo should return mapped citizen info when found`() {
        // Given
        val ssin = "*********"
        val citizenInfoDTO = CitizenInfoDTO().apply {
            firstName = "John"
            lastName = "Doe"
            numBox = BigDecimal(42)
            flagNation = BigDecimal(150)
            iban = "****************"
            address = "Main Street 123 Box A"
            postalCode = "1000"
            paymentMode = 1
            bankAccount = BankAccountDTO().apply {
                iban = "****************"
                bic = null
            }
        }

        val pageResponse = CitizenInfoPageDTO().apply {
            content = listOf(citizenInfoDTO)
            pageNumber = 0
            pageSize = 10
            totalElements = 1
            isFirst = true
            isLast = true
        }

        every { citizenInfoApi.searchCitizenInfo(listOf(ssin), null, "SUMMARY", 0, 10) } returns pageResponse
        every { parseCitizenAddress("Main Street 123 Box A") } returns Triple("Main Street", "123", "A")

        // When
        val result = citizenInfoService.getCitizenInfo(ssin, "Bearer token")

        // Then
        assertThat(result).isNotNull
        assertThat(result?.firstName).isEqualTo("John")
        assertThat(result?.lastName).isEqualTo("Doe")
        assertThat(result?.numbox).isEqualTo(42)
        assertThat(result?.nationality).isEqualTo("150")
        assertThat(result?.iban).isEqualTo("****************")

        assertThat(result?.address).isNotNull
        assertThat(result?.address?.street).isEqualTo("Main Street")
        assertThat(result?.address?.houseNumber).isEqualTo("123")
        assertThat(result?.address?.boxNumber).isEqualTo("A")
        assertThat(result?.address?.country).isNull()
        assertThat(result?.address?.city).isNull()
        assertThat(result?.address?.zipCode).isEqualTo("1000")

        verify(exactly = 1) { citizenInfoApi.searchCitizenInfo(listOf(ssin), null, "SUMMARY", 0, 10) }
        verify(exactly = 1) { parseCitizenAddress("Main Street 123 Box A") }
    }

    @ParameterizedTest
    @MethodSource("nullableFieldsScenarios")
    fun `getCitizenInfo should handle nullable fields`(
        firstName: String?,
        lastName: String?,
        numBox: BigDecimal?,
        nationBcss: BigDecimal?,
        iban: String?,
        address: String?,
        rvaCountryCode: Int?,
        postalCode: String?,
        parsedStreet: String?,
        parsedHouseNumber: String?,
        parsedBoxNumber: String?,
        paymentMode: Int?
    ) {
        // Given
        val ssin = "*********"
        val citizenInfoDTO = CitizenInfoDTO().apply {
            this.firstName = firstName
            this.lastName = lastName
            this.numBox = numBox
            this.flagNation = nationBcss
            this.address = address
            this.postalCode = postalCode
            this.paymentMode = paymentMode
            this.bankAccount = BankAccountDTO().apply {
                this.iban = iban
                this.bic = "THEBIC"
                this.holder = "The Holder"
            }

        }

        val pageResponse = CitizenInfoPageDTO().apply {
            content = listOf(citizenInfoDTO)
            pageNumber = 0
            pageSize = 10
            totalElements = 1
            isFirst = true
            isLast = true
        }

        every { citizenInfoApi.searchCitizenInfo(listOf(ssin), null, "SUMMARY", 0, 10) } returns pageResponse
        if (address != null) {
            every { parseCitizenAddress(citizenInfoDTO.address) } returns Triple<String, String?, String?>(
                parsedStreet!!,
                parsedHouseNumber,
                parsedBoxNumber
            )
        }

        // When
        val result = citizenInfoService.getCitizenInfo(ssin, "Bearer token")

        // Then
        assertThat(result).isNotNull
        assertThat(result?.firstName).isEqualTo(firstName)
        assertThat(result?.lastName).isEqualTo(lastName)
        assertThat(result?.numbox).isEqualTo(if (numBox != null) numBox.toInt() else 0)
        assertThat(result?.nationality).isEqualTo(nationBcss?.toString())
        assertThat(result?.iban).isEqualTo(iban)

        assertThat(result?.address).isNotNull
        assertThat(result?.address?.street).isEqualTo(parsedStreet)
        assertThat(result?.address?.houseNumber).isEqualTo(parsedHouseNumber)
        assertThat(result?.address?.boxNumber).isEqualTo(parsedBoxNumber)
        assertThat(result?.address?.country).isNull()
        assertThat(result?.address?.zipCode).isEqualTo(postalCode)

        verify(exactly = 1) { citizenInfoApi.searchCitizenInfo(listOf(ssin), null, "SUMMARY", 0, 10) }
        verify(exactly = if (address != null) 1 else 0) { parseCitizenAddress(any()) }
    }

    companion object {
        @JvmStatic
        fun nullableFieldsScenarios(): Stream<Arguments> = Stream.of(
            Arguments.of(
                "John", "Doe", null, BigDecimal(150), "****************", "Address", 150, "1000",
                "Street", "Number", "Box",1
            ),
            Arguments.of(
                "John", "Doe", BigDecimal(42), BigDecimal(150), "****************", "Address", 150, "1000",
                "", "123", null,1
            )
        )
    }
}