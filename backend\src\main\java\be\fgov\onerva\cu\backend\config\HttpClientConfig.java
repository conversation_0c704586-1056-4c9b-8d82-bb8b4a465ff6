package be.fgov.onerva.cu.backend.config;

import be.fgov.onerva.barema.api.BaremeApi;
import be.fgov.onerva.cu.backend.rest.client.lookup.wppt.api.LookupApi;
import be.fgov.onerva.person.api.CitizenApi;
import be.fgov.onerva.person.api.CitizenInfoApi;
import be.fgov.onerva.person.invoker.ApiClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.web.client.RestTemplateBuilderConfigurer;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.boot.web.client.RestTemplateCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

@Configuration
@Slf4j
public class HttpClientConfig {

    @Bean
    public RestTemplateBuilder restTemplateBuilder(RestTemplateBuilderConfigurer configurer) {
        return configurer.configure(new RestTemplateBuilder())
                .setReadTimeout(Duration.ofSeconds(20))
                .setConnectTimeout(Duration.ofSeconds(3))
                .additionalInterceptors(new LoggingRequestInterceptor());
    }

    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder restTemplateBuilder) {
        return restTemplateBuilder.build();
    }

    @Bean
    @ConditionalOnProperty(name = "client.security.enabled", havingValue = "true")
    RestTemplateCustomizer globalOAuthClientInterceptor(OAuth2AuthorizedClientManager authorizedClientManager) {
        return restTemplate -> restTemplate.getInterceptors()
                .add(new OAuthClientCredentialsRestTemplateInterceptor(authorizedClientManager));
    }

    @Bean
    public BaremeApi baremeApi(RestTemplate restTemplate, @Value("${barema.url}") String baremaUrl) {
        var apiClient = new be.fgov.onerva.barema.invoker.ApiClient(restTemplate);
        apiClient.setBasePath(baremaUrl);
        return new BaremeApi(apiClient);
    }

    @Bean
    public CitizenApi citizenApi(RestTemplate restTemplate, @Value("${citizen.url}") String citizenBasePath) {
        var apiClient = new ApiClient(restTemplate);
        apiClient.setBasePath(citizenBasePath);
        return new CitizenApi(apiClient);
    }

    @Bean
    public CitizenInfoApi citizenInfoApi(RestTemplate restTemplate, @Value("${citizen.url}") String citizenBasePath) {
        var apiClient = new ApiClient(restTemplate);
        apiClient.setBasePath(citizenBasePath);
        return new CitizenInfoApi(apiClient);
    }

    @Bean
    public LookupApi lookupApiClient(RestTemplate restTemplate, @Value("${lookup.url}") String lookupBasePath) {
        var apiClient = new be.fgov.onerva.cu.backend.rest.client.lookup.wppt.invoker.ApiClient(restTemplate);
        apiClient.setBasePath(lookupBasePath);
        return new LookupApi(apiClient);
    }

    @Bean
    public be.fgov.onerva.registerproxyservice.api.CitizenApi registryApi(RestTemplate restTemplate,
                                                                          @Value("${registry.url}")
                                                                          String registryBasePath) {
        var apiClient = new be.fgov.onerva.registerproxyservice.invoker.ApiClient(restTemplate);
        apiClient.setBasePath(registryBasePath);
        return new be.fgov.onerva.registerproxyservice.api.CitizenApi(apiClient);
    }
}
