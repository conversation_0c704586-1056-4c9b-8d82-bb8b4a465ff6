package be.fgov.onerva.cu.backend.application.port.out

import java.util.UUID
import be.fgov.onerva.cu.backend.application.domain.UpdateCitizen

/**
 * Port interface for updating citizen information in external systems.
 *
 * This interface is part of the hexagonal architecture's ports and adapters pattern,
 * serving as an output port for updating citizen data. It defines methods to update
 * citizen information such as address, nationality, and payment preferences.
 *
 * Implementations of this port handle the communication with external citizen
 * information systems, translating domain models to the format required by these systems.
 *
 * @see UpdateCitizen The domain model containing citizen update information
 * @see be.fgov.onerva.cu.backend.adapter.out.external.citizen.CitizenAdapter The implementation class for this port
 */
interface UpdateCitizenPort {
    /**
     * Updates citizen information in the citizen service.
     *
     * @param requestId The unique identifier of the request that triggered this update
     * @param updateCitizen The domain model containing the citizen information to update
     */
    fun updateCitizenInformation(requestId: UUID, updateCitizen: UpdateCitizen)
}