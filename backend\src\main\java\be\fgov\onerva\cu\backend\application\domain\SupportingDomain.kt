package be.fgov.onerva.cu.backend.application.domain

import java.time.LocalDate

/**
 * Represents a physical address.
 *
 * @property street Street name
 * @property houseNumber House number
 * @property boxNumber Optional box or apartment number
 * @property country Country name
 * @property city City name
 * @property zipCode Postal code
 * @see be.fgov.onerva.cu.backend.application.port.out.LoadCitizenPort
 */
data class AddressNullable(
    val street: String,
    val houseNumber: String,
    val boxNumber: String? = null,
    val country: String?,
    val city: String?,
    val zipCode: String,
    val valueDate: LocalDate?,
)

/**
 * Represents citizen information including their physical address in the system.
 *
 * This data class extends the basic citizen information by including a complete physical address.
 * It combines identification and contact information with detailed address data.
 *
 * This information is coming from the person (citizen) api.
 *
 * @property firstName The citizen's first name
 * @property lastName The citizen's last name
 * @property numbox The unique numeric identifier used for citizen
 * @property nationality The nationality of the citizen
 * @property address The complete physical address details of the citizen
 * @property iban The IBAN number of the citizen
 * @property bic The BIC number of the citizen
 * @property otherPersonName The holder when the account is on another person name
 * @property birthDate The birth date of the citizen
 * @property bankAccountValueDate The value date for the bank account
 * @property paymentMode The payment mode (1 = own account, 2 = other person name, 3 = check)
 *
 * @see AddressNullable
 * @see be.fgov.onerva.cu.backend.application.port.out.LoadCitizenPort
 */
@Deprecated("Use XXX instead")
data class CitizenInfoWithAddress(
    val firstName: String,
    val lastName: String,
    val numbox: Int,
    val nationality: String,
    val address: AddressNullable,
    val iban: String?,
    val bic: String?,
    val otherPersonName: String?,
    val birthDate: LocalDate?,
    val bankAccountValueDate: LocalDate?,
    val paymentMode: Int?,
    val authorized: Boolean?,
    val effectiveDate: LocalDate?,
)

//data class CitizenInfo(
//    val firstName: String,
//    val lastName: String,
//    val numbox: Int,
//    val nationality: String,
//    val address: AddressNullable,
//    val iban: String?,
//    val bic: String?,
//    val otherPersonName: String?,
//    val birthDate: LocalDate?,
//    val bankAccountValueDate: LocalDate?,
//    val paymentMode: Int?,
//    val authorized: Boolean?,
//    val effectiveDate: LocalDate?,
//)

data class HistoricalCitizenSnapshot(
    val firstName: String,
    val lastName: String,
    val numbox: Int?,
    val nationality: String,
    val address: AddressNullable,
    val iban: String?,
    val bic: String?,
    val otherPersonName: String?,
    val birthDate: LocalDate?,
    val bankAccountValueDate: LocalDate?,
    val paymentMode: Int?,
    val authorized: Boolean?,
    val effectiveDate: LocalDate?,
)

data class HistoricalCitizenOnem(
    val firstName: String,
    val lastName: String,
    val numbox: Int,
    val nationality: String,
    val address: AddressNullable,
    val iban: String?,
    val bic: String?,
    val otherPersonName: String?,
    val birthDate: LocalDate?,
    val bankAccountValueDate: LocalDate?,
    val paymentMode: Int?,
    val authorized: Boolean?,
    val effectiveDate: LocalDate?,
)

data class HistoricalCitizenC1(
    val firstName: String,
    val lastName: String,
    val numbox: Int,
    val nationality: String,
    val address: AddressNullable,
    val iban: String?,
    val bic: String?,
    val otherPersonName: String?,
    val birthDate: LocalDate?,
    val bankAccountValueDate: LocalDate?,
    val paymentMode: Int?,
    val authorized: Boolean?,
    val effectiveDate: LocalDate?,
)

data class HistoricalCitizenAuthenticSources(
    val firstName: String,
    val lastName: String,
    val nationality: String,
    val address: AddressNullable,
    val birthDate: LocalDate,
)

data class UpdateCitizen(
    val ssin: String,
    val address: Address,
    val birthDate: LocalDate?,
    val nationality: String,
    val correlationId: String,
    val userName: String,
    val authorized: Boolean?,
    val valueDate: LocalDate?,
)

data class PersonUpdated(
    val id: Long,
    val correlationId: String,
    val ssin: String,
    val success: Boolean,
    val names: String,
)