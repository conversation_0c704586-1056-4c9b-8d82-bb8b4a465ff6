# Complete Unemployment (CU) Project

## Purpose

This project implements the Complete Unemployment eligibility system for ONERVA, providing a comprehensive solution for managing unemployment benefit requests, citizen data validation, and workflow processing.

## Project composition

This project has the following components:

- **backend** - SPRING_BOOT - Core business logic and API services
- **bff** - SPRING_BOOT - Backend for Frontend, API gateway and orchestration
- **cu** - ANGULAR_WEB_COMPONENT - Frontend user interface
- **e2e/cucumber-kotlin-tests** - End-to-end testing with <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>

De<PERSON>ult credentials:

- user: `cu_user`
- password: `password`

## Run

To run the project you have two solutions:

- Start the infra (keycloak and database) first then the app by using
  ```shell
  skaffold dev -m infra
  ```
  and in another shell start the application
  ```shell
  skaffold dev -m app
  ```
- Start the whole project in one command
  ```shell
  skaffold dev
  ```

## Testing

### Unit Tests

Run unit tests for all modules:

```shell
mvn clean test
```

Run unit tests for a specific module:

```shell
mvn clean test -pl backend
mvn clean test -pl bff
mvn clean test -pl common
```

### End-to-End Tests

The project includes comprehensive E2E tests using <PERSON>ucumber with <PERSON><PERSON><PERSON> step definitions.

#### Prerequisites for E2E Tests

1. **Infrastructure must be running**: Start the infrastructure first using skaffold
2. **Services must be accessible**: Ensure backend, BFF, and Keycloak are running
3. **Test data**: The tests will create and clean up their own test data

#### Running E2E Tests Locally

```shell
# Run all E2E tests
mvn clean test -pl e2e/cucumber-kotlin-tests

# Run only smoke tests
mvn test -pl e2e/cucumber-kotlin-tests -Dcucumber.filter.tags="@smoke"

# Run only regression tests
mvn test -pl e2e/cucumber-kotlin-tests -Dcucumber.filter.tags="@regression"

# Run specific feature
mvn test -pl e2e/cucumber-kotlin-tests -Dcucumber.features="src/test/resources/features/change-personal-data.feature"

# Run with CI profile (for CI environment)
mvn test -pl e2e/cucumber-kotlin-tests -Dspring.profiles.active=ci
```

#### E2E Test Reports

After running tests, reports are generated in:

- **HTML Report**: `e2e/cucumber-kotlin-tests/target/cucumber-reports/cucumber.html`
- **JSON Report**: `e2e/cucumber-kotlin-tests/target/cucumber-reports/cucumber.json`
- **JUnit XML**: `e2e/cucumber-kotlin-tests/target/cucumber-reports/cucumber.xml`
- **Enhanced HTML**: `e2e/cucumber-kotlin-tests/target/cucumber-reports/cucumber-html-reports/overview-features.html`

#### Test Structure

- **Features**: Located in `e2e/cucumber-kotlin-tests/src/test/resources/features/`
- **Step Definitions**: Located in `e2e/cucumber-kotlin-tests/src/test/kotlin/.../steps/`
- **Configuration**: `e2e/cucumber-kotlin-tests/src/test/resources/application-ci.yml`
- **Test Context**: Shared data between steps using `TestContext.kt`

### CI/CD Pipeline Testing

The project includes automated E2E testing in the Azure DevOps pipeline:

1. **Build Stage**: Compiles and packages all modules
2. **E2E Test Stage**: Deploys to CI environment and runs all E2E tests
3. **Quality Analysis**: Runs SonarQube analysis with test coverage
4. **Deployment Stages**: Deploys to test, validation, and production environments

#### Pipeline Test Reports

- Test results are published to Azure DevOps Test Results
- Cucumber HTML reports are available as build artifacts
- Coverage reports are integrated with SonarQube

## Troubleshooting

### Common E2E Test Issues

#### 1. Tests Fail with Connection Refused

**Problem**: Tests cannot connect to backend or BFF services.

**Solutions**:

```shell
# Check if services are running
kubectl get pods -n <namespace>

# Check service endpoints
kubectl get svc -n <namespace>

# Check if skaffold is running
skaffold dev -m infra
skaffold dev -m app
```

#### 2. Authentication Failures

**Problem**: Tests fail during Keycloak authentication.

**Solutions**:

- Verify Keycloak is running and accessible
- Check credentials in `application-ci.yml`
- Ensure the test user exists in Keycloak realm
- Verify client configuration in Keycloak

```shell
# Check Keycloak health
curl http://localhost:8080/auth/realms/onemrva-agents/.well-known/openid_configuration
```

#### 3. RabbitMQ Connection Issues

**Problem**: Tests fail when publishing messages to RabbitMQ.

**Solutions**:

- Verify RabbitMQ is running
- Check queue and exchange configuration
- Verify connection parameters in configuration

```shell
# Check RabbitMQ status
kubectl exec -it <rabbitmq-pod> -- rabbitmqctl status

# List queues
kubectl exec -it <rabbitmq-pod> -- rabbitmqctl list_queues
```

#### 4. Test Data Conflicts

**Problem**: Tests fail due to existing test data.

**Solutions**:

- Tests should clean up their own data
- Use unique identifiers for test data
- Check database state before running tests

```shell
# Connect to database and check test data
kubectl exec -it <database-pod> -- sqlcmd -S localhost -U sa -P <password>
```

#### 5. Timeout Issues

**Problem**: Tests timeout waiting for responses.

**Solutions**:

- Increase timeout values in test configuration
- Check if services are under heavy load
- Verify network connectivity between services

#### 6. Maven Build Issues

**Problem**: Maven fails to compile or run tests.

**Solutions**:

```shell
# Clean and rebuild
mvn clean install -DskipTests

# Check for dependency conflicts
mvn dependency:tree

# Run with debug output
mvn test -X -pl e2e/cucumber-kotlin-tests
```

#### 7. Cucumber Report Generation Fails

**Problem**: HTML reports are not generated.

**Solutions**:

- Ensure JSON reports are created first
- Check file permissions in target directory
- Verify maven-cucumber-reporting plugin configuration

```shell
# Check if JSON reports exist
ls -la e2e/cucumber-kotlin-tests/target/cucumber-reports/

# Manually generate reports
mvn net.masterthought:maven-cucumber-reporting:generate -pl e2e/cucumber-kotlin-tests
```

### Environment-Specific Issues

#### Local Development

- Ensure all required services are running via skaffold
- Check port conflicts (default ports: 8080, 8081, 5432, 5672)
- Verify Docker Desktop is running and has sufficient resources

#### CI Environment

- Check Azure DevOps agent capabilities
- Verify environment variables are properly set
- Check Helm chart values for CI environment
- Ensure proper resource limits are configured

### Getting Help

1. **Check Logs**: Always start by checking application and test logs
2. **Verify Configuration**: Ensure all configuration files are correct
3. **Test Isolation**: Run tests individually to isolate issues
4. **Environment Reset**: Sometimes a clean environment restart helps

```shell
# View application logs
kubectl logs -f <pod-name> -n <namespace>

# View test execution logs
mvn test -pl e2e/cucumber-kotlin-tests -Dorg.slf4j.simpleLogger.defaultLogLevel=debug
```
