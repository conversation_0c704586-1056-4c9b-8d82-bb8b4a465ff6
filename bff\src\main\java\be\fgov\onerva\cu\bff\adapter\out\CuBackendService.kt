package be.fgov.onerva.cu.bff.adapter.out

import java.util.UUID
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.util.UriComponentsBuilder
import be.fgov.onerva.cu.bff.rest.server.priv.model.CitizenInformationDetailResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.HistoricalBaremaResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.HistoricalCitizenAuthenticSourcesResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.HistoricalCitizenC1Response
import be.fgov.onerva.cu.bff.rest.server.priv.model.HistoricalCitizenOnemResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.ModeOfPaymentDetailResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.RequestBasicInfoResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.RequestInformationResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.UnionContributionDetailResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateBasicInfoRequest
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateCitizenInformationRequest
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateModeOfPaymentRequest
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateRequestInformationRequest
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateUnionContributionRequest

@Service
class CuBackendService(
    private val restTemplateUtil: RestTemplateUtil,
    @Value("\${backend.base-url:http://localhost:9091}") private val backendBaseUrl: String,
) {

    suspend fun getRequestInformation(requestId: UUID): RequestInformationResponse? = try {
        restTemplateUtil.getForObject(
            buildUrl("/api/requests/$requestId/request-information"), RequestInformationResponse::class.java
        )
    } catch (e: HttpClientErrorException.NotFound) {
        null
    }

    suspend fun getBasicInfo(requestId: UUID, authHeader: String): RequestBasicInfoResponse {
        val url = "$backendBaseUrl/api/requests/$requestId"
        return restTemplateUtil.executeWithAuth<RequestBasicInfoResponse, Void>(
            url,
            HttpMethod.GET,
            authHeader,
            RequestBasicInfoResponse::class.java
        )
    }

    suspend fun getCitizenInfo(requestId: UUID, authHeader: String): CitizenInformationDetailResponse =
        try {
            restTemplateUtil.executeWithAuth<CitizenInformationDetailResponse, Void>(
                "$backendBaseUrl/api/requests/$requestId/citizen-information",
                HttpMethod.GET,
                authHeader,
                CitizenInformationDetailResponse::class.java
            )
        } catch (_: HttpClientErrorException.NotFound) {
            CitizenInformationDetailResponse()
        }

    suspend fun getPaymentInfo(requestId: UUID, authHeader: String): ModeOfPaymentDetailResponse = try {
        restTemplateUtil.executeWithAuth<ModeOfPaymentDetailResponse, Void>(
            "$backendBaseUrl/api/requests/$requestId/mode-of-payment",
            HttpMethod.GET,
            authHeader,
            ModeOfPaymentDetailResponse::class.java
        )
    } catch (e: HttpClientErrorException.NotFound) {
        ModeOfPaymentDetailResponse()
    }

    suspend fun getUnionInfo(requestId: UUID, authHeader: String): UnionContributionDetailResponse = try {
        restTemplateUtil.executeWithAuth<UnionContributionDetailResponse, Void>(
            "$backendBaseUrl/api/requests/$requestId/union-contribution",
            HttpMethod.GET,
            authHeader,
            UnionContributionDetailResponse::class.java
        )
    } catch (e: HttpClientErrorException.NotFound) {
        UnionContributionDetailResponse()
    }

    suspend fun getHistoricalBarema(requestId: UUID, authHeader: String): HistoricalBaremaResponse = try {
        restTemplateUtil.executeWithAuth<HistoricalBaremaResponse, Void>(
            "$backendBaseUrl/api/requests/$requestId/historical/barema",
            HttpMethod.GET,
            authHeader,
            HistoricalBaremaResponse::class.java
        )
    } catch (_: HttpClientErrorException.NotFound) {
        HistoricalBaremaResponse()
    }

    suspend fun getCitizenInfoFromC1(requestId: UUID, authHeader: String): HistoricalCitizenC1Response =
        try {
            restTemplateUtil.executeWithAuth<HistoricalCitizenC1Response, Void>(
                "$backendBaseUrl/api/requests/$requestId/historical/citizen-information/C1",
                HttpMethod.GET,
                authHeader,
                HistoricalCitizenC1Response::class.java
            )
        } catch (_: HttpClientErrorException.NotFound) {
            HistoricalCitizenC1Response()
        }

    suspend fun getCitizenInfoFromOnem(
        requestId: UUID,
        authHeader: String,
    ): HistoricalCitizenOnemResponse = try {
        restTemplateUtil.executeWithAuth<HistoricalCitizenOnemResponse, Void>(
            "$backendBaseUrl/api/requests/$requestId/historical/citizen-information/ONEM",
            HttpMethod.GET,
            authHeader,
            HistoricalCitizenOnemResponse::class.java
        )
    } catch (_: HttpClientErrorException.NotFound) {
        HistoricalCitizenOnemResponse()
    }

    suspend fun getCitizenInfoAuthentic(
        requestId: UUID,
        authHeader: String,
    ): HistoricalCitizenAuthenticSourcesResponse = try {
        restTemplateUtil.executeWithAuth<HistoricalCitizenAuthenticSourcesResponse, Void>(
            "$backendBaseUrl/api/requests/$requestId/historical/citizen-information/AUTHENTIC_SOURCES",
            HttpMethod.GET,
            authHeader,
            HistoricalCitizenAuthenticSourcesResponse::class.java
        )
    } catch (_: HttpClientErrorException.NotFound) {
        HistoricalCitizenAuthenticSourcesResponse()
    }

    suspend fun updateCitizenInformation(
        requestId: UUID,
        citizenInformation: UpdateCitizenInformationRequest?,
        authHeader: String,
    ) {
        citizenInformation?.let {
            restTemplateUtil.executeWithAuth(
                "$backendBaseUrl/api/requests/$requestId/citizen-information",
                HttpMethod.PUT,
                authHeader,
                it
            )
        }
    }

    suspend fun updateModeOfPayment(
        requestId: UUID,
        modeOfPayment: UpdateModeOfPaymentRequest?,
        authHeader: String,
    ) {
        modeOfPayment?.let {
            restTemplateUtil.executeWithAuth(
                "$backendBaseUrl/api/requests/$requestId/mode-of-payment",
                HttpMethod.PUT,
                authHeader,
                it
            )
        }
    }

    suspend fun updateUnionContribution(
        requestId: UUID,
        unionContribution: UpdateUnionContributionRequest?,
        authHeader: String,
    ) {
        unionContribution?.let {
            restTemplateUtil.executeWithAuth(
                "$backendBaseUrl/api/requests/$requestId/union-contribution",
                HttpMethod.PUT,
                authHeader,
                it
            )
        }
    }

    suspend fun updateBasicInfo(
        requestId: UUID,
        updateBasicInfoRequest: UpdateBasicInfoRequest?,
        authHeader: String,
    ) {
        updateBasicInfoRequest?.requestDate?.let { requestDate ->
            val updateRequestInformationRequest = UpdateRequestInformationRequest(requestDate = requestDate)
            restTemplateUtil.executeWithAuth(
                "$backendBaseUrl/api/requests/$requestId/request-information",
                HttpMethod.PUT,
                authHeader,
                updateRequestInformationRequest
            )
        }
    }

    suspend fun assignTaskToUser(
        requestId: UUID,
        authHeader: String,
    ) {
        restTemplateUtil.executeWithAuth(
            "$backendBaseUrl/api/requests/$requestId/assign-user",
            HttpMethod.PUT,
            authHeader,
            null
        )
    }

    private fun buildUrl(path: String): String =
        UriComponentsBuilder.fromUriString(backendBaseUrl).path(path).build().toUriString()
}