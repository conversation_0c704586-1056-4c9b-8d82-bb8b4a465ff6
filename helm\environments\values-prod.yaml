---
global:
  routes:
    host: "cu.prod.paas.onemrva.priv"
kcconfig:
  keycloak:
    url: "https://keycloak.prod.paas.onemrva.priv"
#  realm:
#    clients:
#      cu-frontend:
#        redirectUris:
#        - "https://cu.prod.paas.onemrva.priv/*"
backend:
  secrets:
    spring_datasource_password: ""
  extraEnv:
  - name: SPRING_PROFILES_ACTIVE
    value: prod
  - name: "SPRING_SECURITY_OAUTH2_RESOURCESERVER_JWT_ISSUER-URI"
    value: "https://keycloak.prod.paas.onemrva.priv/realms/onemrva-agents"
  - name: "KEY<PERSON>OAK_AUTH-SERVER-URL"
    value: "https://keycloak.prod.paas.onemrva.priv"
  - name: "KEYCLOAK_CHECKTOKEN"
    value: "true"
  - name: "<PERSON>EYCLOAK_REDIRECT"
    value: "https://cu.prod.paas.onemrva.priv/*"
  - name: "KEY<PERSON>OAK_REALM"
    value: "onemrva-agents"
  - name: "SPRING_DATASOURCE_URL"
    value: ""
  - name: "SPRING_DATASOURCE_USERNAME"
    value: ""
  - name: "SPRING_DATASOURCE_PASSWORD"
    valueFrom:
      secretKeyRef:
        key: "spring_datasource_password"
        name: "cu-backend"
bff:
  secrets:
    spring_datasource_password: ""
  extraEnv:
  - name: SPRING_PROFILES_ACTIVE
    value: prod
  - name: "SPRING_SECURITY_OAUTH2_RESOURCESERVER_JWT_ISSUER-URI"
    value: "https://keycloak.prod.paas.onemrva.priv/realms/onemrva-agents"
  - name: "KEYCLOAK_AUTH-SERVER-URL"
    value: "https://keycloak.prod.paas.onemrva.priv"
  - name: "KEYCLOAK_CHECKTOKEN"
    value: "true"
  - name: "KEYCLOAK_REDIRECT"
    value: "https://cu.prod.paas.onemrva.priv/*"
  - name: "KEYCLOAK_REALM"
    value: "onemrva-agents"
  - name: "SPRING_DATASOURCE_URL"
    value: ""
  - name: "SPRING_DATASOURCE_USERNAME"
    value: ""
  - name: "SPRING_DATASOURCE_PASSWORD"
    valueFrom:
      secretKeyRef:
        key: "spring_datasource_password"
        name: "cu-bff"
cu:
  route:
    path: "/elements"
  readinessProbe:
    httpGet:
      path: "/elements/elements.js"
  livenessProbe:
    httpGet:
      path: "/elements/elements.js"
