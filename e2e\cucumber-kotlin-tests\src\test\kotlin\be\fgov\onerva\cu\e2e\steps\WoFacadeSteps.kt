package be.fgov.onerva.cu.e2e.steps

import be.fgov.onerva.cu.e2e.context.TestContext
import io.cucumber.java.en.Then
import io.cucumber.java.en.When
import org.assertj.core.api.Assertions.assertThat
import org.slf4j.LoggerFactory
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

/** Step definitions for WO-Facade (Wave) task interactions */
@SpringBootTest
@ActiveProfiles("ci")
class WoFacadeSteps(private val testContext: TestContext) {
    private val logger = LoggerFactory.getLogger(WoFacadeSteps::class.java)

    @When("I wait for the request to be processed by the backend")
    fun iWaitForTheRequestToBeProcessedByTheBackend() {
        logger.info("Waiting for backend to process the request")

        val c9Id = testContext.getRequired("c9Id") as Long

        // Simulate waiting for backend processing
        Thread.sleep(5000) // Wait 5 seconds

        // For now, just log that we're waiting
        // TODO: Implement actual polling when WoFacadeApiClient is available
        logger.info("Simulated waiting for backend processing of C9 ID: $c9Id")

        // Set a mock task ID for testing
        testContext.latestTaskId = "MOCK_TASK_${c9Id}"
    }

    @Then("a {string} task should be created for the request")
    fun aTaskShouldBeCreatedForTheRequest(taskType: String) {
        logger.info("Verifying that a '$taskType' task was created")

        val c9Id = testContext.getRequired("c9Id") as Long

        // For now, simulate task creation verification
        // TODO: Implement actual task verification when WoFacadeApiClient is available
        val mockTaskId = "MOCK_TASK_${taskType}_${c9Id}"

        testContext.set("currentTask", mockTaskId)
        testContext.set("currentTaskType", taskType)
        testContext.latestTaskId = mockTaskId
        testContext.latestRequestId = "MOCK_REQUEST_${c9Id}"

        logger.info("Simulated verification of '$taskType' task creation with ID: $mockTaskId")
    }

    @Then("the task status should be {string}")
    fun theTaskStatusShouldBe(expectedStatus: String) {
        logger.info("Verifying task status is '$expectedStatus'")

        val currentTask = testContext.get<String>("currentTask")

        assertThat(currentTask).withFailMessage("No current task found in test context").isNotNull

        // For now, simulate status verification
        // TODO: Implement actual status verification when WoFacadeApiClient is available
        logger.info("Simulated task status verification: $expectedStatus for task: $currentTask")
    }
}
