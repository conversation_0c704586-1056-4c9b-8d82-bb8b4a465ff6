package be.fgov.onerva.cu.backend.application.service

import java.time.LocalDate
import java.util.UUID
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataPersistCommand
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequestReceivedCommand
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequestTreatedCommand
import be.fgov.onerva.cu.backend.application.domain.CreateChangePersonalDataTaskCommand
import be.fgov.onerva.cu.backend.application.domain.RequestInformation
import be.fgov.onerva.cu.backend.application.domain.UpdateChangePersonalDataDecisionCommand
import be.fgov.onerva.cu.backend.application.domain.WaveTaskStatus
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.exception.RequestInvalidStateException
import be.fgov.onerva.cu.backend.application.port.`in`.ChangePersonalDataRequestUseCase
import be.fgov.onerva.cu.backend.application.port.out.BaremaPort
import be.fgov.onerva.cu.backend.application.port.out.C9Port
import be.fgov.onerva.cu.backend.application.port.out.FeatureFlagPort
import be.fgov.onerva.cu.backend.application.port.out.LoadCitizenPort
import be.fgov.onerva.cu.backend.application.port.out.PersistChangePersonalDataPort
import be.fgov.onerva.cu.backend.application.port.out.RequestInformationPort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPort
import be.fgov.onerva.cu.backend.application.service.helpers.WaveTaskHelper
import be.fgov.onerva.cu.common.aop.LogMethodCall
import be.fgov.onerva.cu.common.aop.SensitiveParam
import be.fgov.onerva.cu.common.utils.logger

@Service
class ChangePersonalDataRequestService(
    val loadCitizenPort: LoadCitizenPort,
    val persistChangePersonalDataPort: PersistChangePersonalDataPort,
    val waveTaskPort: WaveTaskPort,
    val c9Port: C9Port,
    val requestInformationPort: RequestInformationPort,
    val baremaPort: BaremaPort,
    val waveTaskHelper: WaveTaskHelper,
    val featureFlagPort: FeatureFlagPort,
) : ChangePersonalDataRequestUseCase {
    private val log = logger

    /**
     * Processes a received change of address request.
     *
     * The processing flow includes:
     * 1. Retrieving the citizen's Numbox using their SSIN
     * 2. Creating and persisting a change of address record
     * 3. Creating a corresponding task in the Wave system
     *
     * @param changePersonalDataRequestReceivedCommand The received change of address request details
     * @throws RuntimeException if the citizen's Numbox cannot be found
     */
    @LogMethodCall
    override fun receivedChangePersonalData(@SensitiveParam changePersonalDataRequestReceivedCommand: ChangePersonalDataRequestReceivedCommand): UUID? {
        if (!evaluateFeatureFlags(
                changePersonalDataRequestReceivedCommand.c9Id,
                changePersonalDataRequestReceivedCommand.type
            )
        ) {
            return null
        }
        val ssin = changePersonalDataRequestReceivedCommand.ssin
        val c9Id = changePersonalDataRequestReceivedCommand.c9Id

        val numbox = loadCitizenPort.getCitizenNumbox(ssin)

        val c9Info = c9Port.loadC9(c9Id)
        val ec1Info = changePersonalDataRequestReceivedCommand.ec1Id?.let { c9Port.loadEC1(it) }

        val type = changePersonalDataRequestReceivedCommand.type
        val changePersonalDataPersistCommand = ChangePersonalDataPersistCommand(
            c9id = c9Id,
            type = type,
            ssin = ssin,
            numbox = numbox,
            receptionDate = c9Info.receptionDate,
            requestDate = c9Info.requestDate,
            paymentInstitution = c9Info.paymentInstitution,
            entityCode = c9Info.entityCode,
            dossierId = "${c9Info.sectOp}-${c9Info.opKey}",
            opKey = c9Info.opKey,
            sectOp = c9Info.sectOp,
            documentType = c9Info.documentType,
            citizenInformation = ec1Info?.citizenInformation,
            modeOfPayment = ec1Info?.modeOfPayment,
            unionContribution = ec1Info?.unionContribution,
            scanUrl = c9Info.scanUrl
        )
        // Persist the entity about the request
        val changePersonalData =
            persistChangePersonalDataPort.persistChangePersonalData(changePersonalDataPersistCommand)

        // Persist the request-Information as well
        val requestInformation = RequestInformation(
            requestDate = c9Info.requestDate
        )
        requestInformationPort.persistRequestInformation(changePersonalData.id, requestInformation)

        val createChangePersonalDataTaskCommand = CreateChangePersonalDataTaskCommand(
            c9id = c9Id,
            type = type,
            ssin = c9Info.ssin,
            numbox = numbox,
            receptionDate = c9Info.receptionDate,
            entityCode = c9Info.entityCode,
            dossierId = "${c9Info.sectOp}-${c9Info.opKey}",
            paymentInstitution = c9Info.paymentInstitution,
            requestDate = c9Info.requestDate,
            sectOp = c9Info.sectOp,
        )

        // Create a Task in Wave
        waveTaskHelper.createChangePersonalDataTask(
            changePersonalData.id,
            createChangePersonalDataTaskCommand
        )
        return changePersonalData.id
    }

    override fun processTreatedChangePersonalData(changePersonalDataTreated: ChangePersonalDataRequestTreatedCommand) {
        if (!evaluateFeatureFlags(
                changePersonalDataTreated.c9Id,
                changePersonalDataTreated.type
            )
        ) {
            return
        }

        val changePersonalDataRequest =
            persistChangePersonalDataPort.getChangePersonalDataByC9Id(changePersonalDataTreated.c9Id)
                ?: throw RequestIdNotFoundException("Request ID not found for c9id: ${changePersonalDataTreated.c9Id}")

        val lastTask = changePersonalDataRequest.changePersonalDataValidateWaveTask
            ?: changePersonalDataRequest.changePersonalDataCaptureWaveTask
            ?: throw RequestInvalidStateException("The request does not have a task ${changePersonalDataRequest.id}")

        val citizenId = loadCitizenPort.getCitizenNumbox(changePersonalDataRequest.ssin)

        val barema = baremaPort.getLatestBarema(citizenId, LocalDate.now())

        val updateChangePersonalDataDecisionCommand = UpdateChangePersonalDataDecisionCommand(
            decisionType = changePersonalDataTreated.decisionType,
            decisionDate = changePersonalDataTreated.decisionDate,
            user = changePersonalDataTreated.user,
            decisionBarema = barema?.barema
        )

        persistChangePersonalDataPort.updateChangePersonalDataWithDecision(
            changePersonalDataRequest.id,
            updateChangePersonalDataDecisionCommand,
        )
        waveTaskPort.updateChangePersonalDataTaskDecision(
            lastTask.processId,
            lastTask.taskId,
            updateChangePersonalDataDecisionCommand
        )

        changePersonalDataRequest.changePersonalDataCaptureWaveTask?.also { task ->
            if (task.status == WaveTaskStatus.OPEN || task.status == WaveTaskStatus.WAITING) {
                waveTaskHelper.assignAndCloseDataCaptureTask(
                    changePersonalDataRequest.id,
                    task.taskId,
                    changePersonalDataTreated.user
                )
            }
        }
        changePersonalDataRequest.changePersonalDataValidateWaveTask?.also { task ->
            if (task.status == WaveTaskStatus.OPEN || task.status == WaveTaskStatus.WAITING) {
                waveTaskHelper.assignAndCloseValidateDataTask(
                    changePersonalDataRequest.id,
                    task.taskId,
                    changePersonalDataTreated.user
                )
            }
        }
        waveTaskPort.closeProcess(lastTask.processId)
    }

    private fun evaluateFeatureFlags(c9id: Long, c9type: String): Boolean {
        val featureEnabled = featureFlagPort.isFeatureEnabled(FeatureFlagPort.CHANGE_OF_ADDRESS_ENABLED, false)
        if (!featureEnabled) {
            log.info("Feature ${FeatureFlagPort.CHANGE_OF_ADDRESS_ENABLED} is not enabled, skipping processing of request ${c9id}")
            return false
        } else {
            log.info("Feature ${FeatureFlagPort.CHANGE_OF_ADDRESS_ENABLED} is enabled, processing request ${c9id}")
        }
        val supportedC9Types = featureFlagPort.getFeatureListValue(FeatureFlagPort.SUPPORTED_C9_TYPES, emptyList())
        if (!supportedC9Types.contains(c9type)) {
            log.info("C9 type ${c9type} is not supported for now, skipping processing of request ${c9id}")
            return false
        }
        return true
    }
}