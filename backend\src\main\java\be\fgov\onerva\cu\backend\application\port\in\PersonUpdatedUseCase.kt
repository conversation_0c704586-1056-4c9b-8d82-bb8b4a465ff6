package be.fgov.onerva.cu.backend.application.port.`in`

import be.fgov.onerva.cu.backend.application.domain.PersonUpdated

/**
 * Use case interface for handling person update events.
 *
 * This port defines the entry point for the application to process notifications
 * when a person's information has been updated in external systems.
 * Implementations should handle the synchronization of person data within the system
 * and trigger any necessary business processes in response to these updates.
 */
interface PersonUpdatedUseCase {
    /**
     * Processes a person updated event.
     *
     * This method is called when the system receives notification that a person's
     * information has been updated in an external system. The implementation should
     * update any relevant local data and trigger appropriate business processes.
     *
     * @param personUpdated The event containing updated person information
     */
    fun receivedPersonUpdated(personUpdated: PersonUpdated)
}