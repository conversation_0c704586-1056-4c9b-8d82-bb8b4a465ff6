import {CommonModule} from "@angular/common";
import {Component, Input} from "@angular/core";
import {MatIcon} from "@angular/material/icon";
import {TranslateModule} from "@ngx-translate/core";
import {OnemrvaThemeModule} from "@onemrvapublic/design-system-theme";
import {RequestBasicInfoResponse} from "@rest-client/cu-bff";
import {FormUtilsService} from "../../services/form-utils.service";
import PushbackStatusEnum = RequestBasicInfoResponse.PushbackStatusEnum;

@Component({
  selector: "lib-cu-closed-or-treated-on-main-frame",
  imports: [
    CommonModule,
    OnemrvaThemeModule,
    TranslateModule,
    MatIcon,
  ],
  templateUrl: "./cu-closed-or-treated-on-main-frame.component.html",
  standalone: true,
  styleUrl: "./cu-closed-or-treated-on-main-frame.component.scss",
})
export class CuClosedOrTreatedOnMainFrameComponent {

  @Input() status?: string;
  @Input() task?: string;
  @Input() decisionType?: string;
  @Input() decisionBarema?: string;
  @Input() nextTaskDescription!: string;
  @Input() nextTaskAction!: string;
  @Input() pushbackStatus!: RequestBasicInfoResponse.PushbackStatusEnum | undefined;

  isTreatedOnMainFrame(): boolean {
    return (this.status == "CLOSED") && !!this.decisionType;
  }

  isClosedAndNotTreated(): boolean {
    return FormUtilsService.isClosedOrWaiting(this.status,this.task) && !this.isTreatedOnMainFrame() && !!this.nextTaskDescription && !!this.nextTaskAction;
  }

  isNotClosed(){
    return this.status != "CLOSED"
  }

  showMainFrameMessage(){
    return this.isNotClosed();
  }

}
