package be.fgov.onerva.cu.backend.application.exception

class InvalidC9Exception(message: String) : RuntimeException(message)

class RequestIdNotFoundException(message: String) : RuntimeException(message)

class CitizenNotFoundException(message: String) : RuntimeException(message)

class WaveException(message: String, cause: Throwable? = null) : RuntimeException(message, cause)

class WaveTaskNotFoundException(message: String) : RuntimeException(message)

class WaveTaskStatusException(message: String) : RuntimeException(message)

class SyncFollowUpNotFoundException(message: String) : RuntimeException(message)

// BAD REQUEST Exceptions

/**
 * Thrown when a request is made with invalid input data.
 * A 400 (BAD REQUEST) will be returned in this case.
 */
class InvalidInputException(message: String) : RuntimeException(message)

/**
 * Thrown when a request is made to act (like close, ...) on a request but the request is not in the right state
 * for doing so.
 * A 400 (BAD REQUEST) will be returned in this case.
 */
class RequestInvalidStateException(message: String) : RuntimeException(message)

/**
 * Thrown when a request is made for an information related to a request but the request does
 * not exist. We are going to return a Bad Request in this case.
 * A 400 (BAD REQUEST) will be returned in this case.
 */
class InvalidRequestIdException(message: String) : RuntimeException(message)

// NOT FOUND Exceptions

/**
 * Thrown when a request is made for an information related to a request but the information does
 * not exist. The request is existing but the specific information is not.
 * A 404 (NOT FOUND) will be returned in this case.
 */
class InformationNotFoundException(message: String) : RuntimeException(message)

class ExternalSourceNotImplementedException(message: String) : RuntimeException(message)

/**
 * Thrown when a request is made for an information related to a request but the information does
 * not exist or is badly structured.
 */
class InvalidExternalDataException(message: String) : RuntimeException(message)

class BaremaNotFoundException(message: String) : RuntimeException(message)