Feature: Data Validation Scenarios
  As a system administrator
  I want to ensure that data validation works correctly
  So that only valid citizen data is processed and stored

  Background:
    Given the test environment is ready

  @smoke @validation
  Scenario: Successful validation with complete and correct data
    # Expected: All validation rules pass when data is complete and correct
    # Actual: System validates data successfully and allows workflow to continue
    Given a new change personal data request is submitted via RabbitMQ
    And the request contains complete citizen information
    When I wait for the request to be processed by the backend
    And I retrieve the aggregate request data for "CHANGE_PERSONAL_DATA_CAPTURE"
    And I close the "CHANGE_PERSONAL_DATA_CAPTURE" task with valid data
    Then a "VALIDATION_DATA" task should be created
    When I retrieve the aggregate request data for "VALIDATION_DATA"
    Then the validation rules should be applied
    And all validation checks should pass
    And the data should be marked as valid

  @regression @validation
  Scenario: Validation with missing required fields
    # Expected: Validation fails when required fields are missing
    # Actual: System identifies missing fields and provides appropriate error messages
    Given a new change personal data request is submitted via RabbitMQ
    And the request contains incomplete citizen information
    When I wait for the request to be processed by the backend
    And I retrieve the aggregate request data for "CHANGE_PERSONAL_DATA_CAPTURE"
    And I close the "CHANGE_PERSONAL_DATA_CAPTURE" task with incomplete data
    Then a "VALIDATION_DATA" task should be created
    When I retrieve the aggregate request data for "VALIDATION_DATA"
    Then the validation rules should be applied
    And validation errors should be present
    And the missing fields should be identified

  @regression @validation
  Scenario: Validation with invalid data formats
    # Expected: Validation fails when data formats are incorrect
    # Actual: System detects format errors and provides specific validation messages
    Given a new change personal data request is submitted via RabbitMQ
    And the request contains citizen information with invalid formats
    When I wait for the request to be processed by the backend
    And I retrieve the aggregate request data for "CHANGE_PERSONAL_DATA_CAPTURE"
    And I close the "CHANGE_PERSONAL_DATA_CAPTURE" task with invalid format data
    Then a "VALIDATION_DATA" task should be created
    When I retrieve the aggregate request data for "VALIDATION_DATA"
    Then the validation rules should be applied
    And format validation errors should be present
    And the invalid fields should be identified with specific error messages

  @regression @validation
  Scenario: Validation with business rule violations
    # Expected: Business rules are enforced during validation
    # Actual: System applies business logic and rejects data that violates rules
    Given a new change personal data request is submitted via RabbitMQ
    And the request contains citizen information that violates business rules
    When I wait for the request to be processed by the backend
    And I retrieve the aggregate request data for "CHANGE_PERSONAL_DATA_CAPTURE"
    And I close the "CHANGE_PERSONAL_DATA_CAPTURE" task with business rule violating data
    Then a "VALIDATION_DATA" task should be created
    When I retrieve the aggregate request data for "VALIDATION_DATA"
    Then the validation rules should be applied
    And business rule violations should be detected
    And appropriate business error messages should be provided

  @regression @validation @edge-case
  Scenario: Validation with edge case data values
    # Expected: System handles edge cases gracefully
    # Actual: Validation processes boundary values and special cases correctly
    Given a new change personal data request is submitted via RabbitMQ
    And the request contains citizen information with edge case values
    When I wait for the request to be processed by the backend
    And I retrieve the aggregate request data for "CHANGE_PERSONAL_DATA_CAPTURE"
    And I close the "CHANGE_PERSONAL_DATA_CAPTURE" task with edge case data
    Then a "VALIDATION_DATA" task should be created
    When I retrieve the aggregate request data for "VALIDATION_DATA"
    Then the validation rules should be applied
    And edge case values should be handled appropriately
    And no unexpected errors should occur

  @regression @validation
  Scenario: Validation with mixed valid and invalid data
    # Expected: System identifies both valid and invalid parts of the data
    # Actual: Validation provides detailed feedback on each field's validity
    Given a new change personal data request is submitted via RabbitMQ
    And the request contains citizen information with mixed validity
    When I wait for the request to be processed by the backend
    And I retrieve the aggregate request data for "CHANGE_PERSONAL_DATA_CAPTURE"
    And I close the "CHANGE_PERSONAL_DATA_CAPTURE" task with mixed validity data
    Then a "VALIDATION_DATA" task should be created
    When I retrieve the aggregate request data for "VALIDATION_DATA"
    Then the validation rules should be applied
    And valid fields should be accepted
    And invalid fields should be rejected with specific errors
    And the validation summary should indicate partial success

  @smoke @validation
  Scenario: Re-validation after data correction
    # Expected: Corrected data passes validation after initial failure
    # Actual: System allows re-validation and accepts corrected data
    Given a new change personal data request is submitted via RabbitMQ
    And the request initially contains invalid citizen information
    When I wait for the request to be processed by the backend
    And I retrieve the aggregate request data for "CHANGE_PERSONAL_DATA_CAPTURE"
    And I close the "CHANGE_PERSONAL_DATA_CAPTURE" task with corrected data
    Then a "VALIDATION_DATA" task should be created
    When I retrieve the aggregate request data for "VALIDATION_DATA"
    Then the validation rules should be applied
    And all validation checks should pass after correction
    And the data should be marked as valid
