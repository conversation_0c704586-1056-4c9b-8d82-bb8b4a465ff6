import {Injectable} from '@angular/core';
import {FormControl} from '@angular/forms';

@Injectable({
  providedIn: 'root',
})
export class FormUtilsService {

  constructor() {
  }

  static checkLengthOfInput(ctrl: FormControl) {
    const maxLengthError = ctrl.errors?.['maxlength'];
    if (!maxLengthError) {
      return null;
    }
    return {
      requiredLength: maxLengthError.requiredLength,
      actualLength: maxLengthError.actualLength,
    };
  }

  static isClosedOrWaiting(status: string | undefined, task: any | undefined): boolean{
    return status === "CLOSED" || task?.state?.code === "Wait";
  }

}
