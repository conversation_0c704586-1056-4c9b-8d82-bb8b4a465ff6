package be.fgov.onerva.cu.bff.mapper

import be.fgov.onerva.cu.bff.model.Address
import be.fgov.onerva.cu.bff.model.CitizenInfoWithAddress
import be.fgov.onerva.cu.bff.model.UnionContribution
import be.fgov.onerva.cu.bff.rest.client.citizen.model.CitizenInfoDTO
import be.fgov.onerva.cu.common.utils.parseCitizenAddress

fun CitizenInfoDTO.toCitizenInfoWithAddress(): CitizenInfoWithAddress {
    val parsedAddressTriple = if (this.address != null)
        parseCitizenAddress(this.address) else null
    return CitizenInfoWithAddress(
        firstName = this.firstName,
        lastName = this.lastName,
        numbox = this.numBox?.toInt() ?: 0,
        nationality = this.flagNation?.toString(),
        iban = this.bankAccount?.iban,
        bic = this.bankAccount?.bic,
        otherPersonName = this.bankAccount?.holder,
        paymentMode = this.paymentMode,
        address = Address(
            street = parsedAddressTriple?.first,
            houseNumber = parsedAddressTriple?.second,
            boxNumber = parsedAddressTriple?.third,
            zipCode = this.postalCode,
            country = null,
            city = null,
        ),
        unionDue = UnionContribution(
            mandateActive = this.unionDue?.mandateActive ?: false,
            validFrom = this.unionDue?.validFrom,
        )
    )
}

