package be.fgov.onerva.cu.bff.adapter.out

import java.util.UUID
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate
import org.springframework.web.util.UriComponentsBuilder
import be.fgov.onerva.cu.bff.exceptions.C9NotFoundException
import be.fgov.onerva.cu.bff.exceptions.CitizenNotFoundException
import be.fgov.onerva.cu.bff.exceptions.InvalidC9Exception
import be.fgov.onerva.cu.bff.exceptions.MissingAuthException
import be.fgov.onerva.cu.bff.exceptions.RequestNotFoundException
import be.fgov.onerva.cu.bff.exceptions.WaveUserNotFoundException
import be.fgov.onerva.cu.bff.rest.server.priv.model.RequestBasicInfoResponse
import be.fgov.onerva.cu.common.aop.LogMethodCall
import be.fgov.onerva.cu.common.utils.DateUtils.formatToYYYYMMDDNoDash

@Service
class RedirectService(
    @Qualifier("userRestTemplate")
    private val restTemplate: RestTemplate,
    private val restTemplateUtil: RestTemplateUtil,
    @Value("\${backend.base-url}")
    private val backendBaseUrl: String,
    @Value("\${c51.url}")
    private val c51BaseUrl: String,
    private val citizenInfoService: CitizenInfoService,
    private val waveUserService: WaveUserService,
    private val c9NavigateMainframeToS24Service: C9NavigateMainframeToS24Service,
    private val c9Service: C9Service,
    @Value("\${regis.url:https://regis.test.paas.onemrva.priv/regis/regis/rew.seam}")
    private val regisUrl: String,
) {
    private val logger = LoggerFactory.getLogger(javaClass)
    @LogMethodCall
    fun getC51Link(requestId: UUID): String {
        val authHeader = restTemplateUtil.captureAuthorizationHeader()
            ?: throw MissingAuthException("Authorization header is missing")

        val token = SecurityContextHolder.getContext().authentication.principal as Jwt
        val ssin = token.claims["ssin"] as String
        val basicInfo = restTemplate.getForObject(
            buildUrl("/api/requests/$requestId"),
            RequestBasicInfoResponse::class.java
        ) ?: throw RequestNotFoundException("Basic info not found for request $requestId")
        val validityDate = basicInfo.dateValid ?: basicInfo.introductionDate
        ?: throw InvalidC9Exception("Neither dateValid nor introductionDate is available")
        val formattedValidityDate = validityDate.formatToYYYYMMDDNoDash()
        val numbox = citizenInfoService.getCitizenInfo(basicInfo.ssin, authHeader)?.numbox
            ?: throw CitizenNotFoundException("Citizen not found for SSIN ${basicInfo.ssin}")
        val operatorCode = try {
            waveUserService.getOperatorCode(ssin)
        } catch (e: WaveUserNotFoundException) {
            throw WaveUserNotFoundException("Wave user not found for SSIN $ssin")
        }
        return "${c51BaseUrl}?numbox=${numbox}&validityDate=${formattedValidityDate}&type=1&operatorCode=${operatorCode}"
    }

    @LogMethodCall
    fun openS24Session(requestId: UUID) {
        val basicInfo = restTemplate.getForObject(
            buildUrl("/api/requests/$requestId"),
            RequestBasicInfoResponse::class.java
        ) ?: throw RequestNotFoundException("Basic info not found for request $requestId")
        val date = basicInfo.dateValid ?: basicInfo.introductionDate
        c9NavigateMainframeToS24Service.openS24Session(basicInfo.ssin, date!!)
    }

    fun getRegisRedirectUrl(requestId: UUID, languageCode: String): String {
        val authHeader = restTemplateUtil.captureAuthorizationHeader()
            ?: throw MissingAuthException("Authorization header is missing")

        val basicInfo = restTemplate.getForObject(
            buildUrl("/api/requests/$requestId"),
            RequestBasicInfoResponse::class.java
        ) ?: throw RequestNotFoundException("Basic info not found for request $requestId")

        val c9Id = basicInfo.c9Id

        val c9Details = try {
            c9Service.getC9Details(c9Id)
        } catch (e: C9NotFoundException) {
            throw InvalidC9Exception("Failed to retrieve C9 details for C9 ID: $c9Id")
        }

        val unemploymentOfficeCode = c9Details.unemploymentOfficeCode.padStart(3, '0')

        val numbox = citizenInfoService.getCitizenInfo(basicInfo.ssin, authHeader)?.numbox
            ?: throw CitizenNotFoundException("Citizen not found for SSIN ${basicInfo.ssin}")

        val token = SecurityContextHolder.getContext().authentication.principal as Jwt

        val userSsin = when (val ssinClaim = token.claims["ssin"]) {
            is String -> ssinClaim
            is Number -> {
                ssinClaim.toString().padStart(11, '0')
            }
            else -> throw IllegalArgumentException("Invalid SSIN format")
        }

        val operatorCode = waveUserService.getOperatorCode(userSsin)

        val dateS24 = basicInfo.introductionDate?.format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"))
            ?: basicInfo.dateValid?.format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"))
            ?: throw IllegalStateException("Neither introductionDate nor dateValid is available")

        val requestDate = basicInfo.requestDate.format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"))

        val languageParam = when (languageCode.lowercase()) {
            "fr" -> "1"
            "nl" -> "2"
            else -> throw IllegalArgumentException("Unsupported language code: $languageCode")
        }

        val uriBuilder = UriComponentsBuilder.fromHttpUrl(regisUrl)
            .queryParam("type", "REW")
            .queryParam("opCode", operatorCode)
            .queryParam("userid", userSsin)
            .queryParam("l", languageParam)
            .queryParam("niss", basicInfo.ssin)
            .queryParam("numbox", numbox)
            .queryParam("date", requestDate)
            .queryParam("dateS24", dateS24)
            .queryParam("WB", unemploymentOfficeCode)
            .queryParam("numop", c9Details.opKey)

        return uriBuilder.build().toUriString()
    }

    private fun buildUrl(path: String): String = UriComponentsBuilder
        .fromUriString(backendBaseUrl)
        .path(path)
        .build()
        .toUriString()
}