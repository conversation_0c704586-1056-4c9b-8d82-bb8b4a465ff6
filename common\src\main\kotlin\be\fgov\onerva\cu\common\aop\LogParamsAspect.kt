package be.fgov.onerva.cu.common.aop

import kotlin.time.DurationUnit
import kotlin.time.measureTimedValue
import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.reflect.MethodSignature
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Aspect that provides method parameter logging functionality for annotated methods.
 *
 * This aspect intercepts methods annotated with [@LogMethodCall] and logs their parameters and execution status.
 * It handles sensitive parameters marked with [@SensitiveParam] by masking their values in the logs.
 *
 * The aspect logs:
 * - Method name and parameters when the method is called
 * - Successful completion of the method
 * - Any exceptions that occur during method execution
 *
 * Example usage:
 * ```kotlin
 * @LogMethodCall
 * fun processUser(username: String, @SensitiveParam password: String) {
 *     // Method implementation
 * }
 * ```
 *
 * This will produce logs like:
 * ```
 * Method processUser called with parameters: username=john, password=******
 * Method processUser completed successfully
 * ```
 */
@Aspect
@Component
class LogParamsAspect {
    /**
     * Intercepts and logs method calls annotated with [@LogMethodCall].
     *
     * @param joinPoint The join point representing the intercepted method call
     * @return The result of the method execution
     * @throws Exception if the intercepted method throws an exception
     */
    @Around("@annotation(be.fgov.onerva.cu.common.aop.LogMethodCall)")
    fun logMethodParams(joinPoint: ProceedingJoinPoint): Any? {
        val logger = LoggerFactory.getLogger(joinPoint.target::class.toString() + ".method")
        val signature = joinPoint.signature as MethodSignature

        // Build parameter log string using Kotlin's sequence and string interpolation
        val params = signature.parameterNames.asSequence().mapIndexed { index, name ->
            val paramValue =
                if (signature.method.parameters[index].isAnnotationPresent(SensitiveParam::class.java)) {
                    "******"
                } else {
                    joinPoint.args.getOrNull(index)?.toString() ?: "null"
                }
            "$name=$paramValue"
        }.joinToString()

        return try {
            logger.info("Method ${signature.name} called with parameters: $params")

            measureTimedValue {
                joinPoint.proceed()
            }.let {
                logger.info(
                    "Method ${signature.name} completed (${
                        it.duration.toString(
                            DurationUnit.SECONDS,
                            3
                        )
                    })"
                )
                it.value
            }
        } catch (e: Exception) {
            logger.error("Method ${signature.name} failed with exception: ${e.javaClass.simpleName}")
            throw e
        }
    }
}