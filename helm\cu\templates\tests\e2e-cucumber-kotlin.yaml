{{ if .Values.e2e.cucumber.enabled }}
apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "cu.fullname" . }}-e2e-cucumber-kotlin"
  labels:
    {{- include "cu.labels" . | nindent 4 }}
    app.kubernetes.io/component: "test_e2e_cucumber"
  annotations:
    "helm.sh/hook": test
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  restartPolicy: Never
  containers:
    - name: cucumber-kotlin-tests
      image: "{{.Values.e2e.cucumber.image.registry | default "docker-alpha.onemrva.priv" }}/{{.Values.e2e.cucumber.image.repository}}:{{.Values.e2e.cucumber.image.tag | default "latest" }}"
      imagePullPolicy: Always
      env:
        - name: SPRING_PROFILES_ACTIVE
          value: "ci"
        - name: KEYCLOAK_BASE_URL
          value: "{{ .Values.security.authserverurl }}"
        - name: KEYCLOAK_REALM
          value: "{{ .Values.security.realm }}"
        - name: KEYCLOAK_CLIENT_ID
          value: "{{ .Values.security.oauth2.client.registration.keycloak.clientid }}"
        - name: KEYCLOAK_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: "{{ include "cu.fullname" . }}-backend-secrets"
              key: spring_backend_keycloak_clientsecret
        - name: KEYCLOAK_USERNAME
          value: "{{ .Values.e2e.cucumber.keycloak.username | default "cu_user" }}"
        - name: KEYCLOAK_PASSWORD
          value: "{{ .Values.e2e.cucumber.keycloak.password | default "password" }}"
        - name: BFF_BASE_URL
          value: "http://{{ include "cu.fullname" . }}-bff:8080"
        - name: WO_FACADE_BASE_URL
          value: "{{ .Values.backend.springConfiguration.client.wo.facade.baseurl }}"
        - name: RABBITMQ_HOST
          value: "{{ .Values.backend.springConfiguration.spring.rabbitmq.host }}"
        - name: RABBITMQ_PORT
          value: "{{ .Values.backend.springConfiguration.spring.rabbitmq.port | default "5672" }}"
        - name: RABBITMQ_USERNAME
          value: "{{ .Values.backend.springConfiguration.spring.rabbitmq.username | default "guest" }}"
        - name: RABBITMQ_PASSWORD
          value: "{{ .Values.backend.springConfiguration.spring.rabbitmq.password | default "guest" }}"
        - name: MY_POD_NAMESPACE
          value: "{{ .Release.Namespace }}"
        - name: MAVEN_OPTS
          value: "-Xmx1024m -XX:MaxPermSize=256m"
      command: ["/bin/sh"]
      args:
        - -c
        - |
          echo "Starting Cucumber E2E Tests..."
          echo "Environment: CI"
          echo "BFF URL: $BFF_BASE_URL"
          echo "WO Facade URL: $WO_FACADE_BASE_URL"
          echo "Keycloak URL: $KEYCLOAK_BASE_URL"
          
          # Wait for services to be ready
          echo "Waiting for BFF service to be ready..."
          until curl -f $BFF_BASE_URL/actuator/health; do
            echo "BFF not ready, waiting..."
            sleep 10
          done
          
          echo "BFF is ready, starting tests..."
          
          # Run the Cucumber tests
          cd /usr/src/app
          mvn clean test -Dspring.profiles.active=ci -Dcucumber.filter.tags="@smoke or @regression"
          
          # Copy test reports to shared volume for artifact collection
          if [ -d "target/cucumber-reports" ]; then
            cp -r target/cucumber-reports/* /test-reports/
            echo "Test reports copied to /test-reports/"
          fi
          
          if [ -d "target/surefire-reports" ]; then
            cp -r target/surefire-reports/* /test-reports/surefire/
            echo "Surefire reports copied to /test-reports/surefire/"
          fi
          
          echo "Cucumber E2E Tests completed"
      volumeMounts:
        - name: test-reports
          mountPath: /test-reports
        - name: maven-cache
          mountPath: /root/.m2
      resources:
        requests:
          memory: "1Gi"
          cpu: "500m"
        limits:
          memory: "2Gi"
          cpu: "1000m"
  volumes:
    - name: test-reports
      emptyDir: {}
    - name: maven-cache
      emptyDir: {}
  {{ end }}
