package be.fgov.onerva.cu.e2e.steps

import be.fgov.onerva.cu.e2e.context.TestContext
import io.cucumber.java.en.Given
import io.cucumber.java.en.Then
import io.cucumber.java.en.When
import org.assertj.core.api.Assertions.assertThat
import org.slf4j.LoggerFactory
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

/** Step definitions for BFF API interactions */
@SpringBootTest
@ActiveProfiles("ci")
class BffSteps(private val testContext: TestContext) {
        private val logger = LoggerFactory.getLogger(BffSteps::class.java)

        @Given("a completed {string} task")
        fun aCompletedTask(taskType: String) {
                logger.info("Setting up scenario with a completed '$taskType' task")

                // This step assumes that a previous scenario has already created and completed the
                // task
                // We need to ensure the test context has the necessary data
                // In a real implementation, this might involve creating test data or finding an
                // existing
                // completed task

                testContext.set("previousTaskType", taskType)
                logger.info("Scenario setup complete for completed '$taskType' task")
        }

        @When("I retrieve the aggregate request data for {string}")
        fun iRetrieveTheAggregateRequestDataFor(taskType: String) {
                logger.info("Retrieving aggregate request data for task type: $taskType")

                val requestId = testContext.latestRequestId

                assertThat(requestId)
                        .withFailMessage("No request ID found in test context")
                        .isNotNull

                // Simulate retrieving aggregate data
                // TODO: Implement actual API call when BffApiClient is available
                val mockAggregateData =
                        mapOf(
                                "requestId" to requestId,
                                "taskType" to taskType,
                                "citizenInformation" to
                                        mapOf(
                                                "firstName" to "John",
                                                "lastName" to "Doe",
                                                "birthDate" to "1990-01-01"
                                        )
                        )

                testContext.set("aggregateData", mockAggregateData)
                testContext.set("currentTaskType", taskType)

                logger.info(
                        "Simulated retrieval of aggregate request data for request ID: $requestId"
                )
        }

        @When("I close the {string} task with valid data")
        fun iCloseTheTaskWithValidData(taskType: String) {
                logger.info("Closing '$taskType' task with valid data")

                val requestId = testContext.latestRequestId

                assertThat(requestId)
                        .withFailMessage("No request ID found in test context")
                        .isNotNull

                // Simulate closing task with valid data
                // TODO: Implement actual API call when BffApiClient is available
                val mockUpdateRequest =
                        mapOf(
                                "taskType" to taskType,
                                "requestId" to requestId,
                                "status" to "COMPLETED"
                        )

                testContext.set("lastUpdateRequest", mockUpdateRequest)

                logger.info("Simulated closing '$taskType' task for request ID: $requestId")
        }

        @When("I close the {string} task")
        fun iCloseTheTask(taskType: String) {
                // Same as closing with valid data, but without specifying "valid data"
                iCloseTheTaskWithValidData(taskType)
        }

        @Then("the response should contain the citizen information")
        fun theResponseShouldContainTheCitizenInformation() {
                logger.info("Verifying that response contains citizen information")

                val aggregateData = testContext.get<Map<String, Any>>("aggregateData")

                assertThat(aggregateData)
                        .withFailMessage("No aggregate data found in test context")
                        .isNotNull

                assertThat(aggregateData!!["citizenInformation"])
                        .withFailMessage("Expected citizen information to be present in response")
                        .isNotNull

                logger.info("Citizen information verification successful")
        }

        @Then("the response should contain the updated citizen information")
        fun theResponseShouldContainTheUpdatedCitizenInformation() {
                logger.info("Verifying that response contains updated citizen information")

                val aggregateData = testContext.get<Map<String, Any>>("aggregateData")

                assertThat(aggregateData)
                        .withFailMessage("No aggregate data found in test context")
                        .isNotNull

                assertThat(aggregateData!!["citizenInformation"])
                        .withFailMessage(
                                "Expected updated citizen information to be present in response"
                        )
                        .isNotNull

                logger.info("Updated citizen information verification successful")
        }

        @Then("the validation rules should be applied")
        fun theValidationRulesShouldBeApplied() {
                logger.info("Verifying that validation rules have been applied")

                val aggregateData = testContext.get<Map<String, Any>>("aggregateData")

                assertThat(aggregateData)
                        .withFailMessage("No aggregate data found in test context")
                        .isNotNull

                logger.info("Validation rules verification successful")
        }

        @Then("the task should be completed successfully")
        fun theTaskShouldBeCompletedSuccessfully() {
                logger.info("Verifying that task was completed successfully")

                val lastUpdateRequest = testContext.get<Map<String, Any>>("lastUpdateRequest")

                assertThat(lastUpdateRequest)
                        .withFailMessage("No update request found in test context")
                        .isNotNull

                logger.info("Task completion verification successful")
        }

        @Then("the request should be marked as completed")
        fun theRequestShouldBeMarkedAsCompleted() {
                logger.info("Verifying that request is marked as completed")

                // This is a placeholder for future implementation
                // In a real scenario, we might check the request status via API
                logger.info("Request completion verification successful")
        }
}
