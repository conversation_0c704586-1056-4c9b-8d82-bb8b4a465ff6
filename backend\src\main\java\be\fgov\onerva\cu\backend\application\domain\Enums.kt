package be.fgov.onerva.cu.backend.application.domain

enum class WaveTaskStatus {
    OPEN,
    CLOSED,
    WAITING,
}

/**
 * Represents the type of annex (attachment) in the system.
 *
 * Possible values:
 * - EC1: Represents an EC1 form attachment
 * - SCANNED_DOCUMENTS: Represents general scanned document attachments
 */
enum class AnnexType {
    EC1,
    SCANNED_DOCUMENTS,
}

enum class IdentityDocumentType {
    ELECTRONIC,
    PAPER,
}

enum class ExternalSource {
    ONEM, // Mainframe via Person API
    AUTHENTIC_SOURCES, // National registry or Regis
    C1, // Internal information
}

enum class DecisionType {
    C2Y,
    C2N,
    C2F,
    C2P,
    C51,
    C9B,
    C2,
    C9NA,
}

enum class SyncFollowUpStatus {
    PENDING, OK, NOK,
}
