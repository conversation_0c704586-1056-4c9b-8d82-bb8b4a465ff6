package be.fgov.onerva.cu.backend.adapter.out.external.citizen

import java.util.UUID
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.adapter.out.mapper.toCitizenInfoWithAddress
import be.fgov.onerva.cu.backend.adapter.out.mapper.toDomainCitizen
import be.fgov.onerva.cu.backend.application.domain.Citizen
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenOnem
import be.fgov.onerva.cu.backend.application.domain.UpdateCitizen
import be.fgov.onerva.cu.backend.application.exception.CitizenNotFoundException
import be.fgov.onerva.cu.backend.application.port.out.LoadCitizenPort
import be.fgov.onerva.cu.backend.application.port.out.UpdateCitizenPort
import be.fgov.onerva.cu.common.aop.LogMethodCall
import be.fgov.onerva.cu.common.aop.SensitiveParam
import be.fgov.onerva.cu.common.utils.logger
import be.fgov.onerva.person.api.CitizenApi
import be.fgov.onerva.person.api.CitizenInfoApi
import be.fgov.onerva.person.rest.model.AddressDTO
import be.fgov.onerva.person.rest.model.CitizenUpdateRequestDTO
import be.fgov.onerva.person.rest.model.PaymentTypeDTO

/**
 * Adapter implementation for loading citizen information from an external API.
 * This class adapts the external citizen API to match the [LoadCitizenPort] interface.
 */
@Service
class CitizenAdapter(
    val citizenApi: CitizenApi,
    val citizenInfoApi: CitizenInfoApi,
) : LoadCitizenPort, UpdateCitizenPort {
    private val log = logger

    @LogMethodCall
    override fun getCitizenNumbox(@SensitiveParam ssin: String): Int =
        citizenApi.getByNiss(ssin)?.numbox ?: throw CitizenNotFoundException("Citizen is not found: $ssin")

    @LogMethodCall
    override fun getCitizenBySsin(@SensitiveParam ssin: String): Citizen {
        val citizen = (citizenApi.getByNiss(ssin)?.toDomainCitizen()
            ?: throw CitizenNotFoundException("Citizen is not found: $ssin"))
        log.debug("getCitizenBySsin: Citizen found for ssin {}: {}", ssin, citizen)
        return citizen
    }

    /**
     * Retrieves complete citizen information including address based on their SSIN.
     *
     * @param ssin The Social Security Identification Number (SSIN) of the citizen
     * @return CitizenInfoWithAddress object containing the citizen's information and address
     * @throws CitizenNotFoundException if no citizen is found with the given SSIN
     */
    @LogMethodCall
    override fun getCitizenWithAddress(@SensitiveParam ssin: String): HistoricalCitizenOnem? {
        val citizenInfoPageDTO = citizenInfoApi.searchCitizenInfo(listOf(ssin), null, "SUMMARY", 0, 10)
        val citizen = citizenInfoPageDTO.content.firstOrNull()
        log.debug("getCitizenWithAddress: Citizen found for ssin {}: {}", ssin, citizen)
        return citizen?.toCitizenInfoWithAddress()
    }

    @LogMethodCall
    override fun updateCitizenInformation(
        requestId: UUID,
        @SensitiveParam updateCitizen: UpdateCitizen,
    ) {
        log.debug("updateCitizenInformation: Updating citizen information for request {}: {}", requestId, updateCitizen)
        val citizenUpdateRequestDTO = CitizenUpdateRequestDTO().also { it ->
            it.address = AddressDTO().also {
                it.street = updateCitizen.address.street
                it.number = updateCitizen.address.houseNumber
                it.box = updateCitizen.address.boxNumber
                it.zip = updateCitizen.address.zipCode.toInt()
                it.city = updateCitizen.address.city
            }
            it.nationalityCode = updateCitizen.nationality.toInt()
            it.paymentType = PaymentTypeDTO.BANK_TRANSFER
            it.unionDue = updateCitizen.authorized
            it.valueDate = updateCitizen.valueDate
            it.correlationId = updateCitizen.correlationId
        }
        log.debug("Updating citizen info for request {} with {}", requestId, citizenUpdateRequestDTO)
        citizenApi.updateCitizen(updateCitizen.ssin, updateCitizen.userName, citizenUpdateRequestDTO)
    }
}