package be.fgov.onerva.cu.e2e.config

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.KotlinModule
import org.springframework.amqp.core.*
import org.springframework.amqp.rabbit.connection.ConnectionFactory
import org.springframework.amqp.rabbit.core.RabbitTemplate
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile

/**
 * RabbitMQ configuration for E2E tests
 * 
 * This configuration sets up the necessary queues, exchanges, and bindings
 * for testing the C9 message flow in the CI environment.
 */
@Configuration
@Profile("ci")
class RabbitMQConfig {

    companion object {
        const val C9_QUEUE_NAME = "cu.c9.queue"
        const val C9_EXCHANGE_NAME = "c9.exchange"
        const val C9_ROUTING_KEY = "cu.c9"
    }

    /**
     * Configure Jackson message converter for RabbitMQ
     * This ensures proper JSON serialization/deserialization of messages
     */
    @Bean
    fun jackson2JsonMessageConverter(): Jackson2JsonMessageConverter {
        val objectMapper = ObjectMapper()
            .registerModule(KotlinModule.Builder().build())
            .registerModule(JavaTimeModule())
        
        return Jackson2JsonMessageConverter(objectMapper)
    }

    /**
     * Configure RabbitTemplate with JSON message converter
     */
    @Bean
    fun rabbitTemplate(
        connectionFactory: ConnectionFactory,
        jackson2JsonMessageConverter: Jackson2JsonMessageConverter
    ): RabbitTemplate {
        val template = RabbitTemplate(connectionFactory)
        template.messageConverter = jackson2JsonMessageConverter
        return template
    }

    /**
     * Define the C9 queue for receiving change personal data messages
     */
    @Bean
    fun c9Queue(): Queue {
        return QueueBuilder
            .durable(C9_QUEUE_NAME)
            .build()
    }

    /**
     * Define the C9 exchange for routing change personal data messages
     */
    @Bean
    fun c9Exchange(): TopicExchange {
        return ExchangeBuilder
            .topicExchange(C9_EXCHANGE_NAME)
            .durable(true)
            .build()
    }

    /**
     * Create binding between C9 queue and exchange
     */
    @Bean
    fun c9Binding(c9Queue: Queue, c9Exchange: TopicExchange): Binding {
        return BindingBuilder
            .bind(c9Queue)
            .to(c9Exchange)
            .with(C9_ROUTING_KEY)
    }

    /**
     * Additional binding for direct queue routing (used by consumers)
     */
    @Bean
    fun c9DirectBinding(c9Queue: Queue, c9Exchange: TopicExchange): Binding {
        return BindingBuilder
            .bind(c9Queue)
            .to(c9Exchange)
            .with(C9_QUEUE_NAME)
    }
}
