import {Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges} from "@angular/core";
import {CommonModule, formatDate} from "@angular/common";
import {FormsModule} from "@angular/forms";
import {MatDialog} from "@angular/material/dialog";
import {MatTableDataSource, MatTableModule} from "@angular/material/table";
import {MatFormFieldModule} from "@angular/material/form-field";
import {MatSelectModule} from "@angular/material/select";
import {MatIconModule} from "@angular/material/icon";
import {MatButtonModule} from "@angular/material/button";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {
    FieldSource,
    HistoricalCitizenAuthenticSourcesResponse,
    HistoricalCitizenC1Response,
    HistoricalCitizenOnemResponse,
} from "@rest-client/cu-bff";
import {DateFormatType, DateUtils} from "../../../date.utils";
import {GeoLookupService} from "../../../http/geo-lookup.service";
import {printFormat as formatIban} from "iban-ts";
import {getValueFromOrigin, Origin} from "../../../model/types";
import {FormUtilsService} from "../../../services/form-utils.service";
import {StringUtils} from "../../../string.utils";
import {CuMaintainValueDialogComponent} from "./maintain-value-dialog/cu-maintain-value-dialog.component";

export interface ConsistencyTableElement {
    id: string;
    label: string;
    encodedValue: string;
    dbValue: string;
    sourceValue: string;
    valueToKeep: string;
    encodedDate: string;
    dbDate: string;
    sourceDate: string;
    isValid?: boolean;
    isEditable?: boolean;
    isConsistent?: boolean;
    selectedValue?: { value: any, fieldName: string, origin?: string } | null;
}

@Component({
    selector: "lib-consistency-table",
    templateUrl: "./consistency-table.component.html",
    styleUrls: ["./consistency-table.component.scss"],
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        MatTableModule,
        MatFormFieldModule,
        MatSelectModule,
        MatIconModule,
        MatButtonModule,
        TranslateModule,
    ],
})
export class ConsistencyTableComponent implements OnInit, OnChanges {
    @Input() displayedColumns!: string[];
    @Input() dataSource: any;
    @Input() dataConsistencyData: any;
    @Input() dataConsistencySelectedValues: { [key: string]: string } = {};
    @Input() taskStatus!: string;
    @Input() task!: string;

    @Output() rowSelectionChanged = new EventEmitter<ConsistencyTableElement>();
    @Output() tableConsistencyChanged = new EventEmitter<boolean>();

    private _language: string = "NL";

    @Input()
    set language(value: string) {
        if (this._language !== value) {
            this._language = value;

            if (value) {
                this.translate.use(value);
            }

            // Refresh the table with new language
            this.updateNationalityDisplays();
        }
    }

    get language(): string {
        return this._language;
    }

    tableDataSource: MatTableDataSource<ConsistencyTableElement> = new MatTableDataSource<ConsistencyTableElement>([]);

    constructor(readonly translate: TranslateService,
                readonly geoLookupService: GeoLookupService,
                readonly matDialog: MatDialog) {
        if (this.language != undefined) {
            this.translate.use(this.language);
        }
    }

    ngOnInit(): void {
        if (!this.dataConsistencySelectedValues) {
            this.dataConsistencySelectedValues = {};
        }

        if (this.dataSource) {
            this.prepareTableData();
        }
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes["language"]) {
            this.updateNationalityDisplays();
        }
        if (changes["dataSource"] && this.dataSource) {
            this.prepareTableData();
        }
    }

    protected isFormClosedOrWaiting(): boolean {
        return FormUtilsService.isClosedOrWaiting(this.taskStatus, this.task);
    }

    private updateNationalityDisplays(): void {
        if (!this.tableDataSource?.data?.length) {
            return;
        }

        const updatedData = [...this.tableDataSource.data];
        let hasChanges = false;

        // Helper function to get nationality display value
        const getNationalityDisplayValue = (nationalitySource: any): string | null => {
            if (!nationalitySource?.nationality) {
                return null;
            }

            const code = typeof nationalitySource.nationality === "object"
                ? nationalitySource.nationality.code
                : nationalitySource.nationality;

            return this.geoLookupService.getEntityDescription(code, this.language);
        };

        // Find nationality item
        const nationalityItem = updatedData.find(item => item.id === "nationality");
        if (nationalityItem) {
            // Update citizen nationality
            const citizenDisplayValue = getNationalityDisplayValue(
                this.dataConsistencyData.c1CitizenInformation,
            );

            if (citizenDisplayValue && nationalityItem.encodedValue !== citizenDisplayValue) {
                nationalityItem.encodedValue = citizenDisplayValue;
                hasChanges = true;
            }

            // Update ONEM nationality
            const onemDisplayValue = getNationalityDisplayValue(
                this.dataConsistencyData.onemCitizenInformation,
            );

            if (onemDisplayValue && nationalityItem.dbValue !== onemDisplayValue) {
                nationalityItem.dbValue = onemDisplayValue;
                hasChanges = true;
            }
        }

        if (hasChanges) {
            this.tableDataSource.data = updatedData;
        }
    }

// Helper method to map external source to origin
    private mapExternalSourceToOrigin(source: string): string {
        switch (source) {
            case "C1":
                return "Employee";
            case "ONEM":
                return "Onem";
            case "AUTHENTIC_SOURCES":
                return "Source";
            default:
                return "Employee"; // Default to Employee
        }
    }

// Helper method to get value by source
    private getValueBySource(
        fieldName: string,
        source: string,
        citizenInfo: HistoricalCitizenC1Response,
        onemInfo: HistoricalCitizenOnemResponse,
        authenticInfo: HistoricalCitizenAuthenticSourcesResponse,
    ): any {
        if (!["C1", "ONEM", "AUTHENTIC_SOURCES"].includes(source)) {
            return null;
        }

        const fieldGetters: Record<string, (citizen: HistoricalCitizenC1Response | HistoricalCitizenOnemResponse | HistoricalCitizenAuthenticSourcesResponse) => any> = {
            birthDate: (citizen) => {
                if (source === "C1") {
                    return citizen.birthDate || (citizen as any).dateOfBirth;
                }
                return citizen.birthDate;
            },
            nationality: (citizen) => {
                if (!citizen.nationality) {
                    return null;
                }

                if (source === "C1" && typeof citizen.nationality === "object" && (citizen.nationality as any).code) {
                    const lang = this.language?.toLowerCase() === "fr" ? "descFr" : "descNl";
                    return (citizen.nationality as any)[lang] || this.geoLookupService.getEntityDescription(
                        (citizen.nationality as any).code,
                        this.language,
                    );
                }

                return this.geoLookupService.getEntityDescription(
                    citizen.nationality,
                    this.language,
                );
            },
            address: (citizen) => citizen.address,
            bankAccount: (citizen) => ({
                iban: "iban" in citizen ? citizen.iban : undefined,
                bic: "bic" in citizen ? citizen.bic : undefined,
            }),
            otherPersonName: (citizen) => "otherPersonName" in citizen ? citizen.otherPersonName : undefined,
        };

        let currentCitizen;
        if (source === "C1") {
            currentCitizen = citizenInfo;
        } else if (source === "ONEM") {
            currentCitizen = onemInfo;
        } else if (source === "AUTHENTIC_SOURCES") {
            currentCitizen = authenticInfo;
        }

        return fieldGetters[fieldName] && currentCitizen ? fieldGetters[fieldName](currentCitizen) : null;
    }

    prepareTableData(): void {
        if (!this.dataConsistencyData) {
            this.tableDataSource = new MatTableDataSource<ConsistencyTableElement>([]);
            this.emitTableConsistency();
            return;
        }

        const {
            unionContribution,
            c1CitizenInformation: citizenInfo,
            onemCitizenInformation: onemInfo,
            authenticCitizenInformation: authenticInfo,
            basicInfo,
            citizenInformation,
            modeOfPayment,
        } = this.dataConsistencyData;

        const c1ValueDate = basicInfo.requestDate || "-";
        const onemAddressValueDate = onemInfo.addressValueDate || "-";
        const onemBankAccountValueDate = onemInfo.bankAccountValueDate || "-";
        const onemUnionContributionValueDate = onemInfo.unionContributionValueDate || "-";
        const authenticSourcesValueDate = authenticInfo.valueDate || "-";

        const fieldSourceMap = this.buildFieldSourceMap(
            citizenInformation?.fieldSources || [],
            modeOfPayment?.fieldSources || [],
            unionContribution?.fieldSources || [],
        );

        const tableData: ConsistencyTableElement[] = [];

        // Process birth date
        let birthDateSelectedValue;
        if (fieldSourceMap.has("birthDate")) {
            const source = fieldSourceMap.get("birthDate")!;
            birthDateSelectedValue = {
                fieldName: "birthDate",
                origin: this.mapExternalSourceToOrigin(source),
                value: this.getValueBySource("birthDate", source, citizenInfo, onemInfo, authenticInfo),
            };
        }

        const birthDate = DateUtils.formatDateTo((citizenInfo.dateOfBirth || citizenInfo.birthDate),
            DateFormatType.ONEMRVA);
        const onemBirthDate = onemInfo?.birthDate ?
            DateUtils.formatDateTo(onemInfo.birthDate, DateFormatType.ONEMRVA) : "-";
        const authenticBirthDate = authenticInfo.birthDate ?
            DateUtils.formatDateTo(authenticInfo.birthDate, DateFormatType.ONEMRVA) :
            "-";

        tableData.push(this.createTableEntry(
            "birthDate",
            "CU_DATA_CONSISTENCY.DC.TABLE.CITIZEN.ROW.BIRTHDATE",
            birthDate,
            onemBirthDate,
            authenticBirthDate,
            birthDate,
            c1ValueDate,
            onemAddressValueDate,
            authenticSourcesValueDate,
            this.compareDates([birthDate, onemBirthDate, authenticBirthDate]),
            birthDateSelectedValue
        ));

        // Process address
        let addressSelectedValue;
        if (fieldSourceMap.has("address")) {
            const source = fieldSourceMap.get("address")!;
            addressSelectedValue = {
                fieldName: "address",
                origin: this.mapExternalSourceToOrigin(source),
                value: StringUtils.formatAddress(this.getValueBySource("address",
                    source,
                    citizenInfo,
                    onemInfo,
                    authenticInfo)),
            };
        }

        const citizenAddress = citizenInfo.address ? StringUtils.formatAddress(citizenInfo.address) : "-";
        const onemAddress = onemInfo?.address ? StringUtils.formatAddress(onemInfo.address) : "-";
        const authenticAddress = authenticInfo?.address ? StringUtils.formatAddress(authenticInfo.address) : "-";

        tableData.push(this.createTableEntry(
            "address",
            "CU_DATA_CONSISTENCY.DC.TABLE.CITIZEN.ROW.ADDRESS",
            citizenAddress,
            onemAddress,
            authenticAddress,
            citizenAddress,
            c1ValueDate,
            onemAddressValueDate,
            authenticSourcesValueDate,
            this.compareAddresses(citizenInfo, onemInfo),
            addressSelectedValue,
        ));

        // Process nationality
        let nationalitySelectedValue;
        if (fieldSourceMap.has("nationality")) {
            const source = fieldSourceMap.get("nationality")!;
            nationalitySelectedValue = {
                fieldName: "nationality",
                origin: this.mapExternalSourceToOrigin(source),
                value: this.getValueBySource("nationality", source, citizenInfo, onemInfo, authenticInfo),
            };
        }

        const citizenNationality = this.formatNationality(citizenInfo.nationality).trim();
        const onemNationality = onemInfo?.nationality ?
            this.geoLookupService.getEntityDescription(onemInfo.nationality, this.language).trim() : "-";
        const authenticNationality = authenticInfo?.nationality ?
            this.geoLookupService.getEntityDescription(authenticInfo.nationality, this.language).trim() :
            "-";

        tableData.push(this.createTableEntry(
            "nationality",
            "CU_DATA_CONSISTENCY.DC.TABLE.CITIZEN.ROW.NATIONALITY",
            citizenNationality,
            onemNationality,
            authenticNationality,
            citizenNationality,
            c1ValueDate,
            onemAddressValueDate,
            authenticSourcesValueDate,
            this.compareNationality(citizenInfo, onemInfo, authenticInfo),
            nationalitySelectedValue
        ));

        // Process bank account
        const bankAccount = citizenInfo.iban || "";
        const bankAccountOnem = onemInfo.iban || "";

        let bankAccountSelectedValue;
        if (bankAccount && fieldSourceMap.has("bankAccount")) {
            const source = fieldSourceMap.get("bankAccount")!;
            bankAccountSelectedValue = {
                fieldName: "bankAccount",
                origin: this.mapExternalSourceToOrigin(source),
                value: this.getValueBySource("bankAccount", source, citizenInfo, onemInfo, authenticInfo),
            };
        }

        const bicValue = citizenInfo.bic || "";
        const bicValueOnem = onemInfo.bic || "";
        const isForeignAccount = bankAccount && !bankAccount.toUpperCase().startsWith("BE");
        const isOnemForeignAccount = bankAccountOnem && !bankAccountOnem.toUpperCase().startsWith("BE");

        const formattedIban = this.formatBankAccount(bankAccount, bicValue, isForeignAccount);
        const formattedIbanOnem = this.formatBankAccount(bankAccountOnem, bicValueOnem, isOnemForeignAccount);

        tableData.push(this.createTableEntry(
            "bankAccount",
            "CU_DATA_CONSISTENCY.DC.TABLE.CITIZEN.ROW.BANKACCOUNT",
            formattedIban,
            formattedIbanOnem,
            "-",
            bankAccount,
            c1ValueDate,
            onemBankAccountValueDate,
            authenticSourcesValueDate,
            this.compareAccounts(
                {iban: bankAccount, bic: bicValue, isForeign: isForeignAccount},
                {iban: bankAccountOnem, bic: bicValueOnem, isForeign: isOnemForeignAccount},
            ),
            bankAccountSelectedValue,
        ));

        // Process account holder
        let otherPersonNameSelectedValue;
        if (fieldSourceMap.has("otherPersonName")) {
            const source = fieldSourceMap.get("otherPersonName")!;
            otherPersonNameSelectedValue = {
                fieldName: "otherPersonName",
                origin: this.mapExternalSourceToOrigin(source),
                value: this.getValueBySource("otherPersonName", source, citizenInfo, onemInfo, authenticInfo),
            };
        }

        const holderName = citizenInfo.otherPersonName || "";
        const holderNameOnem = onemInfo.otherPersonName || "";

        tableData.push(this.createTableEntry(
            "otherPersonName",
            "CU_DATA_CONSISTENCY.DC.TABLE.CITIZEN.ROW.TITULAIRE",
            holderName,
            holderNameOnem,
            "-",
            holderName,
            c1ValueDate,
            onemBankAccountValueDate,
            authenticSourcesValueDate,
            this.compareNames([holderName, holderNameOnem, "-"]),
            otherPersonNameSelectedValue,
        ));

        // Process union contribution
        let unionSelectedValue;
        if (fieldSourceMap.has("cotisation")) {
            const source = fieldSourceMap.get("cotisation")!;
            unionSelectedValue = {
                fieldName: "cotisation",
                origin: this.mapExternalSourceToOrigin(source),
                value: unionContribution,
            };
        }

        const unionContributionValue = unionContribution ?
            this.getUnionContribution(unionContribution.authorized, unionContribution.effectiveDate) : "-";

        const unionContributionOnemValue = onemInfo.unionDue
            ? this.getUnionContribution(onemInfo.unionDue.authorized, onemInfo.unionDue.effectiveDate)
            : "-";

        tableData.push(this.createTableEntry(
            "cotisation",
            "CU_DATA_CONSISTENCY.DC.TABLE.UNION.ROW.CONTRIBUTION",
            unionContributionValue,
            unionContributionOnemValue,
            "-",
            unionContributionValue,
            c1ValueDate,
            onemUnionContributionValueDate,
            authenticSourcesValueDate,
            this.compareUnionContribution(unionContribution, null),
            unionSelectedValue,
        ));

        this.tableDataSource = new MatTableDataSource(tableData);
        this.emitTableConsistency();
    }

    private emitTableConsistency(): void {
        const isTableConsistent = this.checkTableConsistency();
        this.tableConsistencyChanged.emit(isTableConsistent);
    }

    private checkTableConsistency(): boolean {
        if (!this.tableDataSource.data || this.tableDataSource.data.length === 0) {
            return true;
        }

        return this.tableDataSource.data.every(row => {
            return row.isConsistent || (row.selectedValue && row.selectedValue.value !== null);
        });
    }

    /**
     * Helper method to build field source map from various field sources
     */
    private buildFieldSourceMap(
        citizenFieldSources: FieldSource[],
        paymentFieldSources: FieldSource[],
        unionFieldSources: FieldSource[],
    ): Map<string, string> {
        const fieldSourceMap = new Map<string, string>();

        const addFieldSources = (sources: FieldSource[], fieldNameTransform?: (name: string) => string) => {
            sources.forEach(fs => {
                if (fs.fieldName && fs.source) {
                    const fieldName = fieldNameTransform ? fieldNameTransform(fs.fieldName) : fs.fieldName;
                    fieldSourceMap.set(fieldName, fs.source);
                }
            });
        };

        addFieldSources(citizenFieldSources);
        addFieldSources(paymentFieldSources, (name) => name === "account" ? "bankAccount" : name);
        addFieldSources(unionFieldSources);

        return fieldSourceMap;
    }

    /**
     * Helper method to create a table entry
     */
    private createTableEntry(
        id: string,
        label: string,
        encodedValue: string,
        dbValue: string,
        sourceValue: string,
        valueToKeep: string,
        encodedDate: string,
        dbDate: string,
        sourceDate: string,
        isConsistent: boolean,
        selectedValue: any,
    ): ConsistencyTableElement {
        return {
            id,
            label,
            encodedValue,
            dbValue,
            sourceValue,
            valueToKeep,
            encodedDate,
            dbDate,
            sourceDate,
            isValid: true,
            isEditable: false,
            isConsistent,
            selectedValue,
        };
    }

    /**
     * Helper method to format nationality
     */
    private formatNationality(nationality: any): string {
        if (!nationality) {
            return "-";
        }

        if (typeof nationality === "object") {
            return this.language?.toLowerCase() === "fr"
                ? nationality.descFr
                : nationality.descNl;
        } else {
            return this.geoLookupService.getEntityDescription(
                nationality,
                this.language,
            );
        }
    }

    /**
     * Helper method to format bank account
     */
    private formatBankAccount(iban: string, bic: string, isForeign: boolean): string {
        if (!iban) {
            return "";
        }

        let formattedIban = formatIban(iban);

        // Add BIC information for foreign accounts
        if (isForeign && bic) {
            formattedIban += `<span class="bicToCompair">BIC: ${bic}</span>`;
        }

        return formattedIban;
    }

    getModifiedValue(selectedValue: any): any {
        if (!selectedValue) {
            return null;
        }

        if (selectedValue.value && typeof selectedValue.value === "object" && selectedValue.value.iban) {
            if (selectedValue.value.iban && !selectedValue.value.iban.startsWith("BE") && selectedValue.value.bic) {
                return `${selectedValue.value.iban} <span class="bicToCompair">BIC: ${selectedValue.value.bic}</span>`;
            }
            return selectedValue.value.iban;
        }

        return selectedValue?.value ?? null;
    }

    getIcon(isConsistent: boolean): string {
        return isConsistent ? "check_circle" : "warning";
    }

    private normalizeText(text: string): string { // Normalize text: convert to uppercase and remove accents
        if (!text) {
            return "";
        }
        return text.normalize("NFD")
            .replace(/[\u0300-\u036f]/g, "") // Remove accents
            .toUpperCase()
            .trim();
    }

    private compareDates(values: string[]): boolean {
        try {
            const validDateValues = values.filter(val => val && val !== "-");

            if (validDateValues.length <= 1) {
                return true;
            }

            const standardDates = validDateValues.map(dateStr => {
                try {
                    return new Date(dateStr).toISOString().split("T")[0];
                } catch (e) {
                    return dateStr;
                }
            });

            const firstDate = standardDates[0];
            return standardDates.every(date => date === firstDate);
        } catch (error) {
            console.error("Error comparing dates: ", error);
            return false;
        }
    }

    private compareAddresses(citizenInfo: any, onemInfo: any): boolean {
        try {
            if (!citizenInfo || !onemInfo) {
                return true;
            }

            const citizenAddress = citizenInfo.address || citizenInfo;
            const onemAddress = onemInfo?.address;

            if (!onemAddress) {
                return true;
            }

            const normalizeStreet = (street: string) => {
                return street ? this.normalizeText(street) : "";
            };

            const citizenStreet = normalizeStreet(citizenAddress.street);
            const citizenNumber = citizenAddress.houseNumber || citizenAddress.housNbr || "";
            const citizenBox = citizenAddress.boxNumber || citizenAddress.postBox || "";
            const citizenZip = citizenAddress.zipCode || "";

            const onemStreet = normalizeStreet(onemAddress.street);
            const onemNumber = onemAddress.houseNumber || "";
            const onemBox = onemAddress.boxNumber || "";
            const onemZip = onemAddress.zipCode || "";

            return citizenStreet === onemStreet &&
                citizenNumber === onemNumber &&
                citizenBox === onemBox &&
                citizenZip === onemZip;
        } catch (error) {
            console.error("Error comparing addresses:", error);
            return false;
        }
    }

    private compareNationality(citizenInfo: any, onemInfo: any, authenticInfo: any): boolean {
        try {
            if (!citizenInfo ||
                !onemInfo ||
                !authenticInfo ||
                !citizenInfo.nationality ||
                !onemInfo.nationality ||
                !authenticInfo.nationality) {
                return true;
            }

            let citizenCode: string;
            if (typeof citizenInfo.nationality === "object") {
                citizenCode = citizenInfo.nationality.code;
            } else {
                citizenCode = citizenInfo.nationality;
            }

            const onemCode = onemInfo.nationality;

            const authenticCode = authenticInfo.nationality;

            return citizenCode === onemCode && authenticCode === citizenCode && authenticCode === onemCode;
        } catch (error) {
            console.error("Error comparing nationality codes:", error);
            return true;
        }
    }

    private compareAccounts(account1: { iban: string, bic: string, isForeign: boolean },
                            account2: { iban: string, bic: string, isForeign: boolean }): boolean {
        try {
            // If one of the accounts is missing, consider them consistent
            if (!account1.iban || !account2.iban) {
                return true;
            }

            const normalizedIban1 = account1.iban.replace(/\s+/g, "").toUpperCase();
            const normalizedIban2 = account2.iban.replace(/\s+/g, "").toUpperCase();

            const ibansMatch = normalizedIban1 === normalizedIban2;

            // If IBANs don't match, accounts are not consistent
            if (!ibansMatch) {
                return false;
            }

            // For foreign accounts, also check BIC consistency
            if (account1.isForeign || account2.isForeign) {
                // If either account is missing a BIC, it's inconsistent
                if ((account1.isForeign && !account1.bic) || (account2.isForeign && !account2.bic)) {
                    return false;
                }

                if (account1.bic && account2.bic) {
                    const normalizedBic1 = account1.bic.replace(/\s+/g, "").toUpperCase();
                    const normalizedBic2 = account2.bic.replace(/\s+/g, "").toUpperCase();
                    return normalizedBic1 === normalizedBic2;
                }
            }

            return true;
        } catch (error) {
            console.error("Error comparing accounts:", error);
            return false;
        }
    }

    private compareNames(values: string[]): boolean {
        try {
            const validValues = values.filter(val => val && val !== "-");

            if (validValues.length <= 1) {
                return true;
            }

            const normalizedValues = validValues.map(val => this.normalizeText(val));
            const firstValue = normalizedValues[0];
            return normalizedValues.every(val => val === firstValue);
        } catch (error) {
            console.error("Error comparing names:", error);
            return false;
        }
    }

    private getUnionContribution(authorized: boolean, effectiveDate: any): string {
        const authText = authorized ?
            this.translate.instant("CU_DATA_CONSISTENCY.DC.TABLE.UNION.ROW.CONTRIBUTION_AUTHORIZATION") :
            this.translate.instant("CU_DATA_CONSISTENCY.DC.TABLE.UNION.ROW.CONTRIBUTION_NON_AUTHORIZATION");

        const dateText = effectiveDate ?
            `<span class="contributionDate">${this.translate.instant(
                "CU_DATA_CONSISTENCY.DC.TABLE.UNION.ROW.CONTRIBUTION_DATE")} ${effectiveDate}</span>` :
            "";

        return [authText, dateText].filter(Boolean).join(" ").trim();
    }

    private compareUnionContribution(unionContribution: any, unionContributionOnem: any): boolean {
        try {
            if (!unionContribution || !unionContributionOnem) {
                return true;
            }

            return unionContribution.authorized === unionContributionOnem.authorized &&
                unionContribution.effectiveDate === unionContributionOnem.effectiveDate;
        } catch (error) {
            console.error("Error comparing union contributions:", error);
            return false;
        }
    }

    openMaintainValueDialog(row: any) {
        let selectedOriginAsEnum;
        if (row.selectedValue?.origin == "Employee" || row.selectedValue?.origin == "EMPLOYEE") {
            selectedOriginAsEnum =
                Origin.Employee;
        } else if (row.selectedValue?.origin == "Onem" || row.selectedValue?.origin == "ONEM") {
            selectedOriginAsEnum =
                Origin.Onem;
        } else if (row.selectedValue?.origin ==
            "SourceAuthentiques" ||
            row.selectedValue?.origin ==
            "SOURCE_AUTHENTIQUES") {
            selectedOriginAsEnum = Origin.SourceAuthentiques;
        }

        const defaultValue = selectedOriginAsEnum || Origin.Employee;
        let panelClass: string[] = [];
        panelClass.push("medium");
        panelClass.push(`mat-neutral`);
        const dialogRef = this.matDialog.open(CuMaintainValueDialogComponent, {
            data: {
                panelSize: "medium",
                panelType: "mat-neutral",
                row: row,
                language: this.translate.currentLang,
                defaultValue: defaultValue,
            }, panelClass,
        });

        dialogRef.afterClosed().subscribe((result: Origin) => {
            if (result !== null && result !== undefined && Object.values(Origin).includes(result)) {
                row.selectedValue = {
                    fieldName: row.id,
                    origin: result,
                    value: getValueFromOrigin(result, row),
                    modificationDate: formatDate(new Date(), "yyyy-MM-dd", "fr"),
                };
                this.rowSelectionChanged.emit(row);
                this.emitTableConsistency();
            }
        });
    }

    protected readonly FormUtilsService = FormUtilsService;
}