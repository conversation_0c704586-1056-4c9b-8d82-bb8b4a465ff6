# E2E Cucumber Tests for CU Project

This module contains end-to-end tests using <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and Spring Boot.

## Architecture

- **Cucumber-JVM**: BDD framework for writing human-readable test scenarios
- **Kotlin**: All step definitions and support code written in Kotlin
- **<PERSON><PERSON> Client**: Modern Kotlin HTTP client for API calls
- **Spring AMQP**: For RabbitMQ message publishing
- **JUnit 5**: Test runner and reporting
- **Awaitility**: For handling async operations and polling

## Structure

```
src/test/
├── kotlin/
│   └── be/fgov/onerva/cu/e2e/
│       ├── RunCucumberTests.kt        # Test runner
│       ├── api/                       # API clients
│       ├── config/                    # Configuration classes
│       ├── context/                   # Test context
│       └── steps/                     # Step definitions
└── resources/
    ├── features/                      # Cucumber feature files
    └── application-ci.yml             # CI configuration
```

## Running Tests

### Local Development
```bash
# Run all tests
mvn clean test

# Run specific tags
mvn test -Dcucumber.filter.tags="@smoke"

# Run specific feature
mvn test -Dcucumber.features="src/test/resources/features/change-personal-data.feature"
```

### CI Environment
```bash
mvn test -Dspring.profiles.active=ci
```

## Writing New Tests

1. Create a feature file in `src/test/resources/features/`
2. Write scenarios using Gherkin syntax
3. Implement step definitions in `src/test/kotlin/.../steps/`
4. Use TestContext to share data between steps

## Reports

Test reports are generated in:
- HTML: `target/cucumber-reports/cucumber.html`
- JSON: `target/cucumber-reports/cucumber.json`
- JUnit XML: `target/cucumber-reports/cucumber.xml`

## Key Differences from Karate

1. **Type Safety**: Full Kotlin type safety with data classes
2. **Debugging**: Easy to debug step-by-step in IDE
3. **Reusability**: Can share code with main application
4. **Flexibility**: Direct access to Spring beans and configuration

## Prerequisites

- Enable RabbitMQ listeners in CI (remove `@Profile("!ci")` from consumers)
- Ensure test user exists in Keycloak
- Infrastructure deployed in CI environment