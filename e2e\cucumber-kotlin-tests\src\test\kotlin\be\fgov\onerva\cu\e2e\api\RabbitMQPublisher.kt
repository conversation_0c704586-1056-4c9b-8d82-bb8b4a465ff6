package be.fgov.onerva.cu.e2e.api

import be.fgov.onerva.cu.e2e.config.RabbitMQConfig
import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonProperty
import org.slf4j.LoggerFactory
import org.springframework.amqp.rabbit.core.RabbitTemplate
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.util.*

/**
 * Component for publishing RabbitMQ messages in E2E tests
 */
@Component
class RabbitMQPublisher(
    private val rabbitTemplate: RabbitTemplate
) {
    private val logger = LoggerFactory.getLogger(RabbitMQPublisher::class.java)

    /**
     * Publishes a C9 message to the configured exchange and queue
     */
    fun publishC9Message(c9Message: C9TestMessage) {
        logger.info("Publishing C9 message with ID: ${c9Message.id}")
        
        try {
            rabbitTemplate.convertAndSend(
                RabbitMQConfig.C9_EXCHANGE_NAME,
                RabbitMQConfig.C9_ROUTING_KEY,
                c9Message
            )
            
            logger.info("Successfully published C9 message with ID: ${c9Message.id}")
        } catch (e: Exception) {
            logger.error("Failed to publish C9 message with ID: ${c9Message.id}", e)
            throw RuntimeException("Failed to publish C9 message: ${e.message}", e)
        }
    }

    /**
     * Creates a test C9 message for change personal data flow
     */
    fun createChangePersonalDataC9Message(
        ssin: String = generateTestSSIN(),
        c9Id: Long = System.currentTimeMillis()
    ): C9TestMessage {
        return C9TestMessage(
            id = c9Id,
            type = "410", // Change personal data type
            ssin = ssin,
            requestDate = LocalDate.now(),
            introductionDate = LocalDate.now(),
            dateValid = LocalDate.now().plusDays(30),
            opKey = "OP${Random().nextInt(999999)}",
            sectOp = "SO${Random().nextInt(9999)}",
            paymentInstitution = 778899,
            entityCode = "EC${Random().nextInt(999)}",
            unemploymentOffice = 71,
            scanNumber = Random().nextLong(100000, 999999),
            treatmentStatus = "READY_TO_BE_TREATED",
            attestRefs = listOf(
                AttestRef(
                    type = "EC1",
                    id = Random().nextInt(10000, 99999),
                    displayUrl = "https://c9.test.paas.onemrva.priv/api/eC1s/ref2/${ssin}/${c9Id}"
                )
            ),
            ec1Data = EC1Data(
                identity = EC1Identity(
                    inss = ssin,
                    firstName = "John",
                    lastName = "Doe",
                    dateOfBirth = "1990-01-01",
                    street = "Test Street",
                    houseNumber = "123",
                    city = "Brussels",
                    zipCode = "1000",
                    nationality = "BE",
                    country = "BE"
                ),
                modeOfPayment = EC1ModeOfPayment(
                    isMyBankAccount = true,
                    belgianSEPABankAccount = "****************"
                )
            )
        )
    }

    /**
     * Generates a valid test SSIN (Belgian social security number)
     */
    private fun generateTestSSIN(): String {
        val year = Random().nextInt(50, 99) // Birth year (1950-1999)
        val month = String.format("%02d", Random().nextInt(1, 13))
        val day = String.format("%02d", Random().nextInt(1, 29))
        val sequence = String.format("%03d", Random().nextInt(1, 999))
        
        val baseNumber = "$year$month$day$sequence"
        val checksum = 97 - (baseNumber.toLong() % 97)
        
        return "$baseNumber${String.format("%02d", checksum)}"
    }
}

/**
 * Data classes for C9 message structure with proper JSON annotations
 */
data class C9TestMessage(
    val id: Long,
    val type: String,
    val ssin: String,
    @JsonFormat(pattern = "yyyy-MM-dd")
    val requestDate: LocalDate,
    @JsonFormat(pattern = "yyyy-MM-dd")
    val introductionDate: LocalDate,
    @JsonFormat(pattern = "yyyy-MM-dd")
    val dateValid: LocalDate,
    val opKey: String,
    val sectOp: String,
    val paymentInstitution: Int,
    val entityCode: String,
    val unemploymentOffice: Int,
    val scanNumber: Long,
    val treatmentStatus: String,
    val attestRefs: List<AttestRef>,
    val ec1Data: EC1Data? = null
)

data class AttestRef(
    val type: String,
    val id: Int,
    val displayUrl: String
)

data class EC1Data(
    val identity: EC1Identity,
    val modeOfPayment: EC1ModeOfPayment
)

data class EC1Identity(
    val inss: String,
    val firstName: String,
    val lastName: String,
    val dateOfBirth: String,
    val street: String,
    val houseNumber: String,
    val city: String,
    val zipCode: String,
    val nationality: String,
    val country: String
)

data class EC1ModeOfPayment(
    val isMyBankAccount: Boolean,
    val belgianSEPABankAccount: String
)
