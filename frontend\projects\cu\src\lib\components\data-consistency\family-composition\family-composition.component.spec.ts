import {HttpClientTestingModule} from "@angular/common/http/testing";
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FamilyCompositionComponent } from './family-composition.component';
import { TranslateModule, TranslateLoader, TranslateService } from '@ngx-translate/core';
import { HistoricalBaremaResponse } from '@rest-client/cu-bff';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { Observable, of } from 'rxjs';

class MockTranslateLoader implements TranslateLoader {
    getTranslation(lang: string): Observable<any> {
        return of({
            'CU_DATA_CONSISTENCY.DC.FAMILY.FAMILY_SITUATION': 'Family Situation',
            'CU_DATA_CONSISTENCY.DC.FAMILY.LAST_DECISION': 'Last Decision',
            'CU_CALCULATION.CU_BAREMA.BAREMA': 'Barema',
            'CU_CALCULATION.CU_BAREMA.ARTICLE': 'Article'
        });
    }
}

describe('FamilyCompositionComponent', () => {
    let component: FamilyCompositionComponent;
    let fixture: ComponentFixture<FamilyCompositionComponent>;
    let translateService: TranslateService;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                HttpClientTestingModule,
                TranslateModule.forRoot({
                    loader: { provide: TranslateLoader, useClass: MockTranslateLoader }
                }),
                FamilyCompositionComponent
            ],
            schemas: [NO_ERRORS_SCHEMA]
        }).compileComponents();

        fixture = TestBed.createComponent(FamilyCompositionComponent);
        component = fixture.componentInstance;
        translateService = TestBed.inject(TranslateService);

        translateService.setDefaultLang('en');
        translateService.use('en');

        component.barema = {} as HistoricalBaremaResponse;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should handle undefined barema input safely', () => {
        component.barema = undefined;
        expect(() => {
            fixture.detectChanges();
        }).not.toThrow();
    });

    it('should handle empty barema object', () => {
        component.barema = {} as HistoricalBaremaResponse;
        expect(() => {
            fixture.detectChanges();
        }).not.toThrow();
    });

    it('should handle barema with data', () => {
        component.barema = {
            barema: 'Test Barema',
            article: 'Test Article'
        } as HistoricalBaremaResponse;

        expect(() => {
            fixture.detectChanges();
        }).not.toThrow();
    });

    it('should render barema data when provided', () => {
        component.barema = {
            barema: 'Test Barema',
            article: 'Test Article'
        } as HistoricalBaremaResponse;

        fixture.detectChanges();

        expect(component.barema?.barema).toBe('Test Barema');
        expect(component.barema?.article).toBe('Test Article');
    });

});