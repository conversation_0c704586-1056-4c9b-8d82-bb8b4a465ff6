package be.fgov.onerva.cu.backend.application.service

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import be.fgov.onerva.cu.backend.application.domain.Address
import be.fgov.onerva.cu.backend.application.domain.AddressNullable
import be.fgov.onerva.cu.backend.application.domain.BasicRequest
import be.fgov.onerva.cu.backend.application.domain.Citizen
import be.fgov.onerva.cu.backend.application.domain.CitizenInformation
import be.fgov.onerva.cu.backend.application.domain.ExternalSource
import be.fgov.onerva.cu.backend.application.domain.FieldSource
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenAuthenticSources
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenC1
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenOnem
import be.fgov.onerva.cu.backend.application.domain.UpdateCitizenInformation
import be.fgov.onerva.cu.backend.application.exception.InvalidExternalDataException
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.mapper.toDomainAddress
import be.fgov.onerva.cu.backend.application.port.out.CitizenInformationPort
import be.fgov.onerva.cu.backend.application.port.out.FieldSourcePort
import be.fgov.onerva.cu.backend.application.port.out.LoadCitizenPort
import be.fgov.onerva.cu.backend.application.port.out.RequestPort
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.justRun
import io.mockk.verify

@ExtendWith(MockKExtension::class)
internal class CitizenInformationServiceTest {

    @MockK
    private lateinit var loadCitizenPort: LoadCitizenPort

    @MockK
    private lateinit var citizenInformationPort: CitizenInformationPort

    @MockK
    private lateinit var requestPort: RequestPort

    @MockK
    private lateinit var fieldSourcePort: FieldSourcePort

    @MockK
    private lateinit var historicalInformationService: HistoricalInformationService

    @InjectMockKs
    private lateinit var citizenInformationService: CitizenInformationService

    private val testRequestId = UUID.randomUUID()
    private val testEntityId = UUID.randomUUID()
    private val testSsin = "12345678901"
    private val testFirstName = "John"
    private val testLastName = "Doe"
    private val testBirthDate = LocalDate.of(1990, 1, 1)
    private val testNationality = "BE"
    private val testAddress = Address(
        street = "Test Street",
        houseNumber = "123",
        boxNumber = "A",
        country = "Belgium",
        city = "Brussels",
        zipCode = "1000"
    )

    @Nested
    inner class UpdateCitizenInformationTests {

        @Test
        fun `should update existing citizen information`() {
            // Given
            val existingCitizenInfo = CitizenInformation(
                firstName = testFirstName,
                lastName = testLastName,
                birthDate = LocalDate.of(1980, 1, 1),
                nationality = "FR",
                address = Address(
                    street = "Old Street",
                    houseNumber = "456",
                    boxNumber = "B",
                    country = "France",
                    city = "Paris",
                    zipCode = "75001"
                )
            )

            val updateRequest = UpdateCitizenInformation(
                birthDate = testBirthDate,
                nationality = testNationality,
                address = testAddress
            )

            val expectedUpdatedInfo = CitizenInformation(
                firstName = testFirstName,
                lastName = testLastName,
                birthDate = testBirthDate,
                nationality = testNationality,
                address = testAddress
            )

            every { citizenInformationPort.getCitizenInformation(testRequestId) } returns existingCitizenInfo
            justRun { citizenInformationPort.persistCitizenInformation(testRequestId, expectedUpdatedInfo) }

            // When
            citizenInformationService.updateCitizenInformation(testRequestId, updateRequest)

            // Then
            verify { citizenInformationPort.persistCitizenInformation(testRequestId, expectedUpdatedInfo) }
        }

        @Test
        fun `should create new citizen information if not exists`() {
            // Given
            val updateRequest = UpdateCitizenInformation(
                birthDate = testBirthDate,
                nationality = testNationality,
                address = testAddress
            )

            val request = BasicRequest(
                id = testRequestId,
                c9id = 123L,
                ssin = testSsin,
                opKey = "opKey",
                sectOp = "sectOp",
                requestDate = LocalDate.now(),
                decisionType = null,
                decisionBarema = null,
            )

            val citizen = Citizen(
                firstName = testFirstName,
                lastName = testLastName,
                numbox = 1234,
                zipCode = "1000"
            )

            val expectedNewInfo = CitizenInformation(
                firstName = testFirstName,
                lastName = testLastName,
                birthDate = testBirthDate,
                nationality = testNationality,
                address = testAddress
            )

            every { citizenInformationPort.getCitizenInformation(testRequestId) } returns null
            every { requestPort.getRequest(testRequestId) } returns request
            every { loadCitizenPort.getCitizenBySsin(testSsin) } returns citizen
            justRun { citizenInformationPort.persistCitizenInformation(testRequestId, expectedNewInfo) }

            // When
            citizenInformationService.updateCitizenInformation(testRequestId, updateRequest)

            // Then
            verify { citizenInformationPort.persistCitizenInformation(testRequestId, expectedNewInfo) }
        }
    }

    @Test
    fun `should get citizen information by request ID`() {
        // Given
        val expectedInfo = CitizenInformation(
            firstName = testFirstName,
            lastName = testLastName,
            birthDate = testBirthDate,
            nationality = testNationality,
            address = testAddress
        )

        every { citizenInformationPort.getCitizenInformation(testRequestId) } returns expectedInfo

        // When
        val result = citizenInformationService.getCitizenInformation(testRequestId)

        // Then
        assertThat(result).isEqualTo(expectedInfo)
    }

    @Test
    fun `should get citizen information field sources`() {
        // Given
        val expectedFieldSources = listOf(
            FieldSource("birthDate", ExternalSource.C1),
            FieldSource("nationality", ExternalSource.ONEM)
        )

        every { citizenInformationPort.getEntityId(testRequestId) } returns testEntityId
        every {
            fieldSourcePort.getAllFieldSources(
                CitizenInformationService.ENTITY_TYPE,
                testEntityId
            )
        } returns expectedFieldSources

        // When
        val result = citizenInformationService.getCitizenInformationFieldSources(testRequestId)

        // Then
        assertThat(result).isEqualTo(expectedFieldSources)
        verify { citizenInformationPort.getEntityId(testRequestId) }
        verify { fieldSourcePort.getAllFieldSources(CitizenInformationService.ENTITY_TYPE, testEntityId) }
    }

    @Nested
    inner class SelectCitizenInformationFieldSourcesTests {

        private val currentCitizenInformation = CitizenInformation(
            firstName = testFirstName,
            lastName = testLastName,
            birthDate = testBirthDate,
            nationality = testNationality,
            address = testAddress
        )

        private val c1CitizenInfo = HistoricalCitizenC1(
            birthDate = LocalDate.of(1985, 5, 5),
            nationality = "FR",
            firstName = "Jean",
            lastName = "Dupont",
            numbox = 1234,
            iban = "BE1234567890",
            bic = null,
            otherPersonName = null,
            address = AddressNullable(
                street = "C1 Street",
                houseNumber = "789",
                boxNumber = "C",
                country = "France",
                city = "Lyon",
                zipCode = "69000",
                valueDate = LocalDate.of(2022, 1, 1),
            ),
            bankAccountValueDate = LocalDate.of(2022, 1, 1),
            paymentMode = 1,
            authorized = true,
            effectiveDate = LocalDate.of(2022, 1,1)
        )

        private val onemCitizenInfo = HistoricalCitizenOnem(
            birthDate = LocalDate.of(1987, 7, 7),
            nationality = "DE",
            firstName = "Hans",
            lastName = "Müller",
            numbox = 1234,
            iban = "DE1234567890",
            bic = null,
            otherPersonName = null,
            address = AddressNullable(
                street = "ONEM Street",
                houseNumber = "101",
                boxNumber = "D",
                country = "Germany",
                city = "Berlin",
                zipCode = "10115",
                valueDate = LocalDate.of(2022, 1, 1),
            ),
            bankAccountValueDate = LocalDate.of(2022, 1, 1),
            paymentMode = 1,
            authorized = true,
            effectiveDate = LocalDate.of(2022, 1,1)
        )

        private val authenticCitizenInfo = HistoricalCitizenAuthenticSources(
            birthDate = LocalDate.of(1989, 7, 7),
            nationality = "BE",
            firstName = "Karel",
            lastName = "Janssens",
            address = AddressNullable(
                street = "AUTHENTIC Street",
                houseNumber = "10",
                boxNumber = "B",
                country = "Germany",
                city = "Berlin",
                zipCode = "10115",
                valueDate = LocalDate.of(2020, 1, 1),
            ),
        )

        @Test
        fun `should throw exception when citizen information not found`() {
            // Given
            val fieldSources = listOf(FieldSource("birthDate", ExternalSource.C1))

            every { citizenInformationService.getCitizenInformation(testRequestId) } returns null

            // When/Then
            assertThatThrownBy {
                citizenInformationService.selectCitizenInformationFieldSources(testRequestId, fieldSources)
            }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessageContaining("Citizen information not found for request ID")
        }

        @Test
        fun `should throw exception when using invalid source for birthDate`() {
            // Given
            val fieldSources = listOf(FieldSource("birthDate", ExternalSource.ONEM))

            every { citizenInformationService.getCitizenInformation(testRequestId) } returns currentCitizenInformation
            every {
                historicalInformationService.getHistoricalCitizenC1(
                    testRequestId,
                )
            } returns c1CitizenInfo
            every {
                historicalInformationService.getHistoricalCitizenOnem(
                    testRequestId,
                )
            } returns onemCitizenInfo
            every {
                historicalInformationService.getHistoricalCitizenAuthenticSources(
                    testRequestId,
                )
            } returns authenticCitizenInfo

            // When/Then
            assertThatThrownBy {
                citizenInformationService.selectCitizenInformationFieldSources(testRequestId, fieldSources)
            }
                .isInstanceOf(InvalidExternalDataException::class.java)
                .hasMessageContaining("ONEM does not have birthDate")
        }

        @ParameterizedTest
        @ValueSource(strings = ["birthDate", "nationality", "address"])
        fun `should select field source from authentic sources`(fieldName: String) {
            // Given
            val fieldSources = listOf(FieldSource(fieldName, ExternalSource.AUTHENTIC_SOURCES))

            every { citizenInformationService.getCitizenInformation(testRequestId) } returns currentCitizenInformation
            every {
                historicalInformationService.getHistoricalCitizenC1(
                    testRequestId,
                )
            } returns c1CitizenInfo
            every {
                historicalInformationService.getHistoricalCitizenOnem(
                    testRequestId,
                )
            } returns onemCitizenInfo
            every {
                historicalInformationService.getHistoricalCitizenAuthenticSources(
                    testRequestId,
                )
            } returns authenticCitizenInfo
            every { citizenInformationPort.getEntityId(testRequestId) } returns testEntityId
            justRun { citizenInformationPort.persistCitizenInformation(any(), any()) }
            justRun { fieldSourcePort.setMultipleFieldSources(any(), any(), any()) }

            // When
            citizenInformationService.selectCitizenInformationFieldSources(testRequestId, fieldSources)

            // Then
            verify { citizenInformationPort.persistCitizenInformation(testRequestId, any()) }
            verify {
                fieldSourcePort.setMultipleFieldSources(
                    CitizenInformationService.ENTITY_TYPE,
                    testEntityId,
                    fieldSources
                )
            }
        }

        @Test
        fun `should select birthDate from C1 source`() {
            // Given
            val fieldSources = listOf(FieldSource("birthDate", ExternalSource.C1))

            val expectedCitizenInfo = currentCitizenInformation.copy(birthDate = c1CitizenInfo.birthDate!!)

            every { citizenInformationService.getCitizenInformation(testRequestId) } returns currentCitizenInformation
            every {
                historicalInformationService.getHistoricalCitizenC1(
                    testRequestId,
                )
            } returns c1CitizenInfo
            every {
                historicalInformationService.getHistoricalCitizenOnem(
                    testRequestId,
                )
            } returns onemCitizenInfo
            every {
                historicalInformationService.getHistoricalCitizenAuthenticSources(
                    testRequestId,
                )
            } returns authenticCitizenInfo
            every { citizenInformationPort.getEntityId(testRequestId) } returns testEntityId
            justRun { citizenInformationPort.persistCitizenInformation(any(), any()) }
            justRun { fieldSourcePort.setMultipleFieldSources(any(), any(), any()) }

            // When
            citizenInformationService.selectCitizenInformationFieldSources(testRequestId, fieldSources)

            // Then
            verify { citizenInformationPort.persistCitizenInformation(testRequestId, expectedCitizenInfo) }
            verify {
                fieldSourcePort.setMultipleFieldSources(
                    CitizenInformationService.ENTITY_TYPE,
                    testEntityId,
                    fieldSources
                )
            }
        }

        @Test
        fun `should select nationality from ONEM source`() {
            // Given
            val fieldSources = listOf(FieldSource("nationality", ExternalSource.ONEM))

            val expectedCitizenInfo = currentCitizenInformation.copy(nationality = onemCitizenInfo.nationality)

            every { citizenInformationService.getCitizenInformation(testRequestId) } returns currentCitizenInformation
            every {
                historicalInformationService.getHistoricalCitizenC1(
                    testRequestId,
                )
            } returns c1CitizenInfo
            every {
                historicalInformationService.getHistoricalCitizenOnem(
                    testRequestId,
                )
            } returns onemCitizenInfo
            every {
                historicalInformationService.getHistoricalCitizenAuthenticSources(
                    testRequestId,
                )
            } returns authenticCitizenInfo
            every { citizenInformationPort.getEntityId(testRequestId) } returns testEntityId
            justRun { citizenInformationPort.persistCitizenInformation(any(), any()) }
            justRun { fieldSourcePort.setMultipleFieldSources(any(), any(), any()) }

            // When
            citizenInformationService.selectCitizenInformationFieldSources(testRequestId, fieldSources)

            // Then
            verify { citizenInformationPort.persistCitizenInformation(testRequestId, expectedCitizenInfo) }
            verify {
                fieldSourcePort.setMultipleFieldSources(
                    CitizenInformationService.ENTITY_TYPE,
                    testEntityId,
                    fieldSources
                )
            }
        }

        @Test
        fun `should select address from C1 source`() {
            // Given
            val fieldSources = listOf(FieldSource("address", ExternalSource.C1))

            val expectedCitizenInfo = currentCitizenInformation.copy(address = c1CitizenInfo.address.toDomainAddress())

            every { citizenInformationService.getCitizenInformation(testRequestId) } returns currentCitizenInformation
            every {
                historicalInformationService.getHistoricalCitizenC1(
                    testRequestId,
                )
            } returns c1CitizenInfo
            every {
                historicalInformationService.getHistoricalCitizenOnem(
                    testRequestId,
                )
            } returns onemCitizenInfo
            every {
                historicalInformationService.getHistoricalCitizenAuthenticSources(
                    testRequestId,
                )
            } returns authenticCitizenInfo
            every { citizenInformationPort.getEntityId(testRequestId) } returns testEntityId
            justRun { citizenInformationPort.persistCitizenInformation(any(), any()) }
            justRun { fieldSourcePort.setMultipleFieldSources(any(), any(), any()) }

            // When
            citizenInformationService.selectCitizenInformationFieldSources(testRequestId, fieldSources)

            // Then
            verify { citizenInformationPort.persistCitizenInformation(testRequestId, expectedCitizenInfo) }
            verify {
                fieldSourcePort.setMultipleFieldSources(
                    CitizenInformationService.ENTITY_TYPE,
                    testEntityId,
                    fieldSources
                )
            }
        }

        @Test
        fun `should select address from ONEM source`() {
            // Given
            val fieldSources = listOf(FieldSource("address", ExternalSource.ONEM))

            val expectedCitizenInfo =
                currentCitizenInformation.copy(address = onemCitizenInfo.address.toDomainAddress())

            every { citizenInformationService.getCitizenInformation(testRequestId) } returns currentCitizenInformation
            every {
                historicalInformationService.getHistoricalCitizenC1(
                    testRequestId,
                )
            } returns c1CitizenInfo
            every {
                historicalInformationService.getHistoricalCitizenOnem(
                    testRequestId,
                )
            } returns onemCitizenInfo
            every {
                historicalInformationService.getHistoricalCitizenAuthenticSources(
                    testRequestId,
                )
            } returns authenticCitizenInfo
            every { citizenInformationPort.getEntityId(testRequestId) } returns testEntityId
            justRun { citizenInformationPort.persistCitizenInformation(any(), any()) }
            justRun { fieldSourcePort.setMultipleFieldSources(any(), any(), any()) }

            // When
            citizenInformationService.selectCitizenInformationFieldSources(testRequestId, fieldSources)

            // Then
            verify { citizenInformationPort.persistCitizenInformation(testRequestId, expectedCitizenInfo) }
            verify {
                fieldSourcePort.setMultipleFieldSources(
                    CitizenInformationService.ENTITY_TYPE,
                    testEntityId,
                    fieldSources
                )
            }
        }

        @Test
        fun `should select multiple field sources from different sources`() {
            // Given
            val fieldSources = listOf(
                FieldSource("birthDate", ExternalSource.C1),
                FieldSource("nationality", ExternalSource.ONEM),
                FieldSource("address", ExternalSource.AUTHENTIC_SOURCES)
            )

            val expectedCitizenInfo = currentCitizenInformation.copy(
                birthDate = c1CitizenInfo.birthDate!!,
                nationality = onemCitizenInfo.nationality,
                address = authenticCitizenInfo.address.toDomainAddress()
            )

            every { citizenInformationService.getCitizenInformation(testRequestId) } returns currentCitizenInformation
            every {
                historicalInformationService.getHistoricalCitizenC1(
                    testRequestId,
                )
            } returns c1CitizenInfo
            every {
                historicalInformationService.getHistoricalCitizenOnem(
                    testRequestId,
                )
            } returns onemCitizenInfo
            every {
                historicalInformationService.getHistoricalCitizenAuthenticSources(
                    testRequestId,
                )
            } returns authenticCitizenInfo
            every { citizenInformationPort.getEntityId(testRequestId) } returns testEntityId
            justRun { citizenInformationPort.persistCitizenInformation(any(), any()) }
            justRun { fieldSourcePort.setMultipleFieldSources(any(), any(), any()) }

            // When
            citizenInformationService.selectCitizenInformationFieldSources(testRequestId, fieldSources)

            // Then
            verify { citizenInformationPort.persistCitizenInformation(testRequestId, expectedCitizenInfo) }
            verify {
                fieldSourcePort.setMultipleFieldSources(
                    CitizenInformationService.ENTITY_TYPE,
                    testEntityId,
                    fieldSources
                )
            }
        }
    }

    @Nested
    inner class AddressNullableExtensionTests {

        @Test
        fun `should convert AddressNullable to Address with non-null values`() {
            // Given
            val addressNullable = AddressNullable(
                street = "Test Street",
                houseNumber = "123",
                boxNumber = "A",
                country = "Belgium",
                city = "Brussels",
                zipCode = "1000",
                valueDate = LocalDate.of(2022, 1, 1)
            )

            // When
            val result = addressNullable.toDomainAddress()

            // Then
            assertThat(result).isEqualTo(
                Address(
                    street = "Test Street",
                    houseNumber = "123",
                    boxNumber = "A",
                    country = "Belgium",
                    city = "Brussels",
                    zipCode = "1000",
                )
            )
        }

        @Test
        fun `should convert AddressNullable to Address with null values replaced by defaults`() {
            // Given
            val addressNullable = AddressNullable(
                street = "Test Street",
                houseNumber = "123",
                boxNumber = null,
                country = null,
                city = null,
                zipCode = "1000",
                valueDate = LocalDate.of(2022, 1, 1),
            )

            // When
            val result = addressNullable.toDomainAddress()

            // Then
            assertThat(result).isEqualTo(
                Address(
                    street = "Test Street",
                    houseNumber = "123",
                    boxNumber = null,
                    country = "",
                    city = "",
                    zipCode = "1000"
                )
            )
        }
    }
}
