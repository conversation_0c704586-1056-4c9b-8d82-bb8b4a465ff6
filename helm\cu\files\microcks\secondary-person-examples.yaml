apiVersion: mocks.microcks.io/v1alpha1
kind: APIExamples
metadata:
  name: Person API
  version: '1.0.0'
operations:
  'GET /citizen/{niss}':
    Citizen-***********:
      request:
        parameters:
          niss: '***********'
      response:
        status: '200'
        mediaType: application/json
        body:
          niss: "***********"
          firstname: EL MUSTAPHA
          lastname: KARIM
          numbox: 47000
          zipCode: 1180
          pensionNumber: 188313070

  'GET /citizen/info':
    # paymentMode = 2 - holder Toto Titi
    CitizenInfo-***********:
      request:
        parameters:
          ssins: [ '***********' ]
          pageNumber: 0
          pageSize: 10
          dataReturned: 'SUMMARY'
      response:
        status: '200'
        mediaType: application/json
        body:
          pageSize: 10
          pageNumber: 0
          totalPage: 1
          totalElements: 1
          isFirst: true
          isLast: true
          content:
            - flagNation: 150
              id: 5544
              ssin: "***********"
              numPens: 0
              lastName: person
              firstName: test
              address: GUIDO GEZELLESTRAAT 26 12
              postalCode: "1060"
              numBox: 3078582
              OP: 0
              unemploymentOffice: 711
              iban: "DEPRECATED"
              language: fr
              sex: "1"
              rvaCountryCode: 1
              flagVCpte: 0
              email: <EMAIL>
              emailReg: ""
              flagToPurge: ""
              lastModifDate: ********
              telephoneOnem: ""
              gsmOnem: ""
              telephoneReg: ""
              gsmReg: ""
              deceasedDate: null
              addressObj:
                street: GUIDO GEZELLESTRAAT
                number: 26
                box: 12
                zip: "1060"
                city: "Brussels"
                countryCode: 150
                validFrom: 2022-10-12
              bankAccount:
                iban: ****************
                bic: null
                holder: Toto Titi
                validFrom: 2022-01-01
              paymentMode: 2
              bisNumber:
                - "88888"
              unionDue:
                mandateActive: false
                validFrom: 2025-01-01

  'PUT /citizen/{niss}':
    UpdateCitizenInformation-***********:
      request:
        parameters:
          niss: '***********'
          username: 'cu_user'
      response:
        status: '204'
