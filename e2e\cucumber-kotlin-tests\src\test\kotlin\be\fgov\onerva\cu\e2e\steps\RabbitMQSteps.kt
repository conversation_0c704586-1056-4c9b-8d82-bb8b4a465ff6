package be.fgov.onerva.cu.e2e.steps

import be.fgov.onerva.cu.e2e.api.RabbitMQPublisher
import be.fgov.onerva.cu.e2e.context.TestContext
import io.cucumber.java.en.Given
import org.slf4j.LoggerFactory
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

/** Step definitions for RabbitMQ message publishing */
@SpringBootTest
@ActiveProfiles("ci")
class RabbitMQSteps(
        private val rabbitMQPublisher: RabbitMQPublisher,
        private val testContext: TestContext
) {
    private val logger = LoggerFactory.getLogger(RabbitMQSteps::class.java)

    @Given("a new change personal data request is submitted via RabbitMQ")
    fun aNewChangePersonalDataRequestIsSubmittedViaRabbitMQ() {
        logger.info("Publishing C9 message to RabbitMQ")

        val c9Message = rabbitMQPublisher.createChangePersonalDataC9Message()

        try {
            rabbitMQPublisher.publishC9Message(c9Message)

            // Store the C9 ID for later reference
            testContext.set("c9Id", c9Message.id)
            testContext.set("ssin", c9Message.ssin)

            logger.info("Successfully published C9 message with ID: ${c9Message.id}")
        } catch (e: Exception) {
            logger.error("Failed to publish C9 message", e)
            throw RuntimeException("Failed to publish C9 message: ${e.message}", e)
        }
    }
}
