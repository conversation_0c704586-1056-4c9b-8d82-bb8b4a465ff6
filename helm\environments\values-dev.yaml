---
global:
  routes:
    enabled: false
  metrics:
    enabled: false
  logging:
    enabled: false
#frontend:
#  resources:
#    requests:
#      cpu: "1"
#      memory: "2Gi"
#    limits:
#      cpu: "1"
#      memory: "2Gi"
neometadata:
  enabled: false
e2e:
  enabled: true
infra:
  enabled: true
  rabbitmq:
    enabled: true
  keycloak:
    enabled: true
  mssql:
    enabled: true
kcconfig:
  realm:
    clients:
      cu-backend:
        secret: "cu-backend-secret"
      cu-frontend:
        public: true
        redirectUris:
          - http://localhost:4300/*
    users:
      cu_admin:
        enabled: true
      cu_user:
        enabled: true
      cu-fr-user:
        email: cu-fr-user@example
        firstName: Cu-Employee
        lastName: Fr-User
        password: password
        attributes:
          ouCode:
            - 71200
          language:
            - fr
          ssin:
            - 85050599890
      cu-nl-user:
        email: cu-nl-user@example
        firstName: Cu-Employee
        lastName: Nl-User
        password: password
        attributes:
          ouCode:
            - 71200
          language:
            - nl
          ssin:
            - 85050599890
      unauthorized:
        email: unauthorized@example
        firstName: unauthorized
        lastName: User
        password: pass
        attributes:
          ouCode:
            - 71200
          language:
            - fr

backend:
  metrics:
    enabled: false
  secrets:
    spring_datasource_password: "D3vD3vD3v$"
  springConfiguration:
    client:
      security:
        enabled: false
    onerva:
      metrics:
        enabled: false
      observability:
        prometheus:
          enabled: false
        otel:
          enabled: false
    spring:
      profiles:
        active: dev
      security:
        oauth2:
          resourceserver:
            jwt:
              issuer-uri: http://cu-infra-keycloak-http.cu.svc.cluster.local/realms/onemrva-agents
          client:
            provider:
              keycloak:
                authorization-uri: http://localhost:8082/realms/onemrva-agents/protocol/openid-connect/auth
                issuer-uri: http://cu-infra-keycloak-http.cu.svc.cluster.local/realms/onemrva-agents
      datasource:
        url: *********************************************
        username: sa
      rabbitmq:
        host: rabbitmq
        port: 5672
        username: guest  # Default RabbitMQ username
        password: guest  # Default RabbitMQ password
        listener:
          simple:
            retry:
              enabled: true
              initial-interval: 2s
              max-attempts: 5
              max-interval: 5s
              multiplier: 1.5
      liquibase:
        contexts: ddl,dml,ddl-dev,dml-dev
    lookup:
      url: http://services.onemrva.priv/lookupwpptservice/rest

    keycloak:
      authserverurl: http://localhost:8082/
      checkToken: false
      redirect: http://localhost:4300
      realm: onemrva-agents

    woThirdPartyApi:
      url: http://cu-infra-fake-wo-api.cu.svc.cluster.local:8080/thirdParties/v1
    woOrganizationalChartApi:
      url: http://cu-infra-fake-wo-api.cu.svc.cluster.local:8080/rest/organizationalChart/nsso/v2/services
    werkomgeving:
      enabled: true
      mock: true
      woFacadeApi:
        url: http://cu-infra-woconfigurator.cu.svc.cluster.local/api
    citizen:
      url: http://cu-infra-microcks.cu.svc.cluster.local/rest/Person+API/1.0.0
    c9Api:
      url: "http://cu-infra-microcks.cu.svc.cluster.local/rest/C9+REST+API/1.0.0"
    registry:
      url: http://cu-infra-microcks.cu.svc.cluster.local/rest/Register+Proxy+Service+public+API/1.0.0
    flagsmith:
      api:
        key: Ltu7TyR4Gf8LrGpvDig4sy
        url: https://flagsmith.prod.paas.onemrva.priv/api/v1/

  extraEnv:
    - name: SPRING_DATASOURCE_PASSWORD
      valueFrom:
        secretKeyRef:
          key: spring_datasource_password
          name: cu-backend
  livenessProbe:
    initialDelaySeconds: 240
    timeoutSeconds: 20
  readinessProbe:
    initialDelaySeconds: 120
    periodSeconds: 25
    timeoutSeconds: 10
    failureThreshold: 10
bff:
  metrics:
    enabled: false
  springConfiguration:
    client:
      security:
        enabled: false
    onerva:
      metrics:
        enabled: false
      observability:
        prometheus:
          enabled: false
        otel:
          enabled: false
    spring:
      profiles:
        active: dev
      security:
        oauth2:
          resourceserver:
            jwt:
              issuer-uri: "http://cu-infra-keycloak-http.cu.svc.cluster.local/realms/onemrva-agents"
          client:
            provider:
              keycloak:
                authorization-uri: "http://localhost:8082/realms/onemrva-agents/protocol/openid-connect/auth"
                issuer-uri: "http://cu-infra-keycloak-http.cu.svc.cluster.local/realms/onemrva-agents"
    keycloak:
      auth-server-url: "http://localhost:8082"
      checktoken: false
      redirect: "http://localhost:4300"
      realm: "onemrva-agents"
    lookup:
      url: http://services.onemrva.priv/lookupwpptservice/rest
    citizen:
      url: http://localhost:9999/rest/Person+API/1.0.0
    backend:
      base-url: http://localhost:9091
    woUserFacadeApi:
        url: http://cu-infra-woconfigurator.cu.svc.cluster.local/api
    c51:
      url: https://proc51.test.paas.onemrva.priv/proc51/home.jsf
    c9Api:
      url: "http://cu-infra-microcks.cu.svc.cluster.local/rest/C9+REST+API/1.0.0"
    regis:
      url: https://regis.test.paas.onemrva.priv/regis/regis/rew.seam

woconfig:
  wo_backend:
    enabled: true
    fullnameOverride: "cu-infra-woconfigurator"
    fakeWoUrl: "http://cu-infra-fake-wo-api.cu.svc.cluster.local:8080"
  configClient:
    enabled: false
    baseUrl: "http://cu-infra-woconfigurator"
    oauth:
      enabled: false
  wo-fake:
    enabled: true
    wo_api:
      enabled: true

cors:
  allowedOrigins: http://localhost:4300

microcks:
  enabled: true
  global:
    microcksUrl: http://cu-infra-microcks.cu.svc.cluster.local:80

wiremock:
  enabled: true
