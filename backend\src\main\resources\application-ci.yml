spring:
  # CI-specific configuration for backend module
  profiles:
    active: ci
  
  # RabbitMQ configuration for CI environment
  rabbitmq:
    host: rabbitmq
    port: 5672
    username: guest
    password: guest
    virtual-host: onemrva
    listener:
      simple:
        auto-startup: true
        retry:
          enabled: true
          initial-interval: 2s
          max-attempts: 5
          max-interval: 5s
          multiplier: 1.5
  
  # Database configuration for CI
  datasource:
    url: ***************************************
    username: sa
    # Password is injected via environment variable
  
  # Liquibase configuration for CI
  liquibase:
    enabled: true
    contexts: ddl,dml,ddl-ci,dml-ci
  
  # Security configuration for CI
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://cu-ci-kc.test.paas.onemrva.priv/realms/onemrva-agents
      client:
        registration:
          keycloak:
            client-id: cu-backend
            # Client secret is injected via environment variable
            scope: openid
            authorization-grant-type: client_credentials
        provider:
          keycloak:
            issuer-uri: https://cu-ci-kc.test.paas.onemrva.priv/realms/onemrva-agents

# RabbitMQ OAuth configuration for CI
rabbitmq:
  enabled: true
  oauth:
    token-endpoint-uri: https://cu-ci-kc.test.paas.onemrva.priv/realms/onemrva-agents/protocol/openid-connect/token
    client-id: ${spring.security.oauth2.client.registration.keycloak.client-id}
    client-secret: ${spring.security.oauth2.client.registration.keycloak.client-secret}

# Keycloak configuration for CI
keycloak:
  auth-server-url: https://cu-ci-kc.test.paas.onemrva.priv
  realm: onemrva-agents

# Logging configuration for CI environment
logging:
  level:
    root: INFO
    be.fgov.onerva.cu.backend: DEBUG
    org.springframework.amqp: DEBUG
    org.springframework.amqp.rabbit: DEBUG
    be.fgov.onerva.rabbitmq: DEBUG
    # Enable SQL logging for debugging in CI
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Management endpoints for CI monitoring
management:
  endpoints:
    web:
      exposure:
        include: "*"
        exclude: "heapdump"
  endpoint:
    health:
      show-details: always

# Application-specific configuration for CI
app:
  jobs:
    lookup:
      # Disable scheduled jobs in CI to avoid interference with tests
      cron: "-"
