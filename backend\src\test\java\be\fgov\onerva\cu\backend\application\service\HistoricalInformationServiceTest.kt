package be.fgov.onerva.cu.backend.application.service

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.application.domain.Address
import be.fgov.onerva.cu.backend.application.domain.AddressNullable
import be.fgov.onerva.cu.backend.application.domain.Barema
import be.fgov.onerva.cu.backend.application.domain.BasicRequest
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequest
import be.fgov.onerva.cu.backend.application.domain.CitizenInfoWithAddress
import be.fgov.onerva.cu.backend.application.domain.CitizenInformation
import be.fgov.onerva.cu.backend.application.domain.ExternalSource
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenOnem
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenSnapshot
import be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.domain.RequestInformation
import be.fgov.onerva.cu.backend.application.domain.Snapshot
import be.fgov.onerva.cu.backend.application.domain.UnionContribution
import be.fgov.onerva.cu.backend.application.domain.WaveTaskRevisionNumbers
import be.fgov.onerva.cu.backend.application.exception.CitizenNotFoundException
import be.fgov.onerva.cu.backend.application.exception.ExternalSourceNotImplementedException
import be.fgov.onerva.cu.backend.application.exception.RequestInvalidStateException
import be.fgov.onerva.cu.backend.application.port.out.BaremaPort
import be.fgov.onerva.cu.backend.application.port.out.CitizenInformationPort
import be.fgov.onerva.cu.backend.application.port.out.LoadCitizenPort
import be.fgov.onerva.cu.backend.application.port.out.ModeOfPaymentPort
import be.fgov.onerva.cu.backend.application.port.out.RegistryPort
import be.fgov.onerva.cu.backend.application.port.out.RequestInformationPort
import be.fgov.onerva.cu.backend.application.port.out.RequestPort
import be.fgov.onerva.cu.backend.application.port.out.SnapshotPort
import be.fgov.onerva.cu.backend.application.port.out.UnionContributionPort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPersistencePort
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import io.mockk.verifySequence

@ExtendWith(MockKExtension::class)
class HistoricalInformationServiceTest {

    @MockK
    private lateinit var requestPort: RequestPort

    @MockK
    private lateinit var loadCitizenPort: LoadCitizenPort

    @MockK
    private lateinit var snapshotPort: SnapshotPort

    @MockK
    private lateinit var waveTaskPersistencePort: WaveTaskPersistencePort

    @MockK
    private lateinit var citizenInformationPort: CitizenInformationPort

    @MockK
    private lateinit var modeOfPaymentPort: ModeOfPaymentPort

    @MockK
    private lateinit var unionContributionPort: UnionContributionPort

    @MockK
    private lateinit var baremaPort: BaremaPort

    @MockK
    private lateinit var requestInformationPort: RequestInformationPort

    @MockK
    private lateinit var registryPort: RegistryPort

    @InjectMockKs
    private lateinit var historicalInformationService: HistoricalInformationService

    @Nested
    inner class GetCitizenInformationTests {

        private val requestId = UUID.randomUUID()
        private val ssin = "123456789"
        private val request = BasicRequest(
            id = requestId,
            c9id = 12345L,
            ssin = ssin,
            opKey = "OP123",
            sectOp = "SO123",
            requestDate = LocalDate.now(),
            decisionType = null,
            decisionBarema = null,
        )

        private val citizenInfo = HistoricalCitizenOnem(
            firstName = "John",
            lastName = "Doe",
            numbox = 123,
            nationality = "BE",
            birthDate = null,
            address = AddressNullable(
                street = "Main Street",
                houseNumber = "42",
                country = "BE",
                city = "Brussels",
                zipCode = "1000",
                valueDate = LocalDate.of(2022, 1, 1)
            ),
            iban = "BE123456789",
            bic = null,
            otherPersonName = null,
            bankAccountValueDate = LocalDate.of(2022, 1, 1),
            paymentMode = 1,
            authorized = true,
            effectiveDate = LocalDate.of(2022, 1, 1)
        )
        private val citizenSnapshot = HistoricalCitizenSnapshot(
            firstName = "John",
            lastName = "Doe",
            numbox = 123,
            nationality = "BE",
            birthDate = null,
            address = AddressNullable(
                street = "Main Street",
                houseNumber = "42",
                country = "BE",
                city = "Brussels",
                zipCode = "1000",
                valueDate = LocalDate.of(2022, 1, 1)
            ),
            iban = "BE123456789",
            bic = null,
            otherPersonName = null,
            bankAccountValueDate = LocalDate.of(2022, 1, 1),
            paymentMode = 1,
            authorized = true,
            effectiveDate = LocalDate.of(2022, 1, 1)
        )

        @Test
        fun `should load citizen information and save to snapshot when snapshot is null`() {
            // Given
            every { requestPort.getRequest(requestId) } returns request
            every {
                snapshotPort.getCitizenInformationSnapshot(
                    requestId, ExternalSource.ONEM
                )
            } returns Snapshot.NotFound
            every { loadCitizenPort.getCitizenWithAddress(ssin) } returns citizenInfo
            every {
                snapshotPort.saveCitizenInformationSnapshot(
                    requestId,
                    ExternalSource.ONEM,
                    any(),
                )
            } returns Unit

            // When
            val result = historicalInformationService.getHistoricalCitizenOnem(
                requestId
            )

            // Then
            assertThat(result).isEqualTo(citizenInfo)
            verify(exactly = 1) { requestPort.getRequest(requestId) }
            verify(exactly = 1) { snapshotPort.getCitizenInformationSnapshot(requestId, ExternalSource.ONEM) }
            verify(exactly = 1) { loadCitizenPort.getCitizenWithAddress(ssin) }
            verify(exactly = 1) {
                snapshotPort.saveCitizenInformationSnapshot(
                    requestId, ExternalSource.ONEM, citizenSnapshot
                )
            }
        }

        @Test
        fun `should load citizen information and save to snapshot when snapshot is not readonly`() {
            // Given
            every { requestPort.getRequest(requestId) } returns request

            val snapshot = Snapshot.Found(citizenSnapshot, readonly = false)
            every { snapshotPort.getCitizenInformationSnapshot(requestId, ExternalSource.ONEM) } returns snapshot

            every { loadCitizenPort.getCitizenWithAddress(ssin) } returns citizenInfo
            every {
                snapshotPort.saveCitizenInformationSnapshot(
                    requestId, ExternalSource.ONEM, any()
                )
            } returns Unit

            // When
            val result = historicalInformationService.getHistoricalCitizenOnem(
                requestId
            )

            // Then
            assertThat(result).isEqualTo(citizenInfo)
            verify(exactly = 1) { requestPort.getRequest(requestId) }
            verify(exactly = 1) { snapshotPort.getCitizenInformationSnapshot(requestId, ExternalSource.ONEM) }
            verify(exactly = 1) { loadCitizenPort.getCitizenWithAddress(ssin) }
            verify(exactly = 1) {
                snapshotPort.saveCitizenInformationSnapshot(
                    requestId, ExternalSource.ONEM, citizenSnapshot
                )
            }
        }

        @Test
        fun `should throw CitizenNotFoundException when citizen is not found in external source`() {
            // Given
            every { requestPort.getRequest(requestId) } returns request
            every {
                snapshotPort.getCitizenInformationSnapshot(
                    requestId, ExternalSource.ONEM
                )
            } returns Snapshot.NotFound
            every { loadCitizenPort.getCitizenWithAddress(ssin) } returns null

            // When/Then
            assertThatExceptionOfType(CitizenNotFoundException::class.java).isThrownBy {
                historicalInformationService.getHistoricalCitizenOnem(
                    requestId
                )
            }.withMessage("Citizen is not found for requestID: $requestId")

            verify(exactly = 1) { requestPort.getRequest(requestId) }
            verify(exactly = 1) { snapshotPort.getCitizenInformationSnapshot(requestId, ExternalSource.ONEM) }
            verify(exactly = 1) { loadCitizenPort.getCitizenWithAddress(ssin) }
            verify(exactly = 0) { snapshotPort.saveCitizenInformationSnapshot(any(), any(), any()) }
        }

        @Test
        fun `should return citizen information from C1 source when available`() {
            // Given
            val requestId = UUID.randomUUID()
            val requestDateInC1 = LocalDate.now()

            val revisionNumbers = WaveTaskRevisionNumbers(
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4
            )

            val citizenInformation = CitizenInformation(
                firstName = "John",
                lastName = "Doe",
                birthDate = LocalDate.of(1990, 1, 1),
                nationality = "BE",
                address = Address(
                    street = "Main Street",
                    houseNumber = "42",
                    boxNumber = "A",
                    zipCode = "1000",
                    city = "Brussels",
                    country = "BE"
                ),
            )

            val modeOfPayment = ModeOfPayment(
                iban = "****************",
                bic = "BBRUBEBB",
                otherPersonName = null,
            )

            val unionContribution = UnionContribution(
                authorized = true, effectiveDate = null
            )

            val expectedCitizenInfoWithAddress = CitizenInfoWithAddress(
                firstName = "John",
                lastName = "Doe",
                numbox = 0,
                nationality = "BE",
                birthDate = null,
                address = AddressNullable(
                    street = "Main Street",
                    houseNumber = "42",
                    boxNumber = "A",
                    zipCode = "1000",
                    city = "Brussels",
                    country = "BE",
                    valueDate = requestDateInC1,
                ),
                iban = "****************",
                bic = null,
                otherPersonName = null,
                bankAccountValueDate = requestDateInC1,
                paymentMode = 1,
                authorized = true,
                effectiveDate = LocalDate.of(2022, 1, 1)
            )

            every {
                waveTaskPersistencePort.getCitizenInformationRevision(requestId, "CHANGE_PERSONAL_DATA_CAPTURE")
            } returns revisionNumbers
            every {
                citizenInformationPort.getCitizenInformationForRevision(requestId, 1)
            } returns citizenInformation
            every {
                modeOfPaymentPort.getModeOfPaymentForRevision(requestId, 2)
            } returns modeOfPayment
            every {
                unionContributionPort.getUnionContributionForRevision(requestId, 3)
            } returns unionContribution
            every { requestInformationPort.getRequestInformationForRevision(requestId, 4) } returns RequestInformation(
                requestDate = requestDateInC1,
            )

            // When
            val result = historicalInformationService.getHistoricalCitizenC1(
                requestId
            )

            // Then
            assertThat(result).isNotNull
            assertThat(result.firstName).isEqualTo(expectedCitizenInfoWithAddress.firstName)
            assertThat(result.lastName).isEqualTo(expectedCitizenInfoWithAddress.lastName)
            assertThat(result.numbox).isEqualTo(expectedCitizenInfoWithAddress.numbox)
            assertThat(result.nationality).isEqualTo(expectedCitizenInfoWithAddress.nationality)
            assertThat(result.iban).isEqualTo(expectedCitizenInfoWithAddress.iban)
            assertThat(result.address).isEqualTo(expectedCitizenInfoWithAddress.address)

//            verify(exactly = 1) { requestPort.getRequest(requestId) }
            verify(exactly = 1) {
                waveTaskPersistencePort.getCitizenInformationRevision(requestId, "CHANGE_PERSONAL_DATA_CAPTURE")
            }
            verify(exactly = 1) {
                citizenInformationPort.getCitizenInformationForRevision(requestId, 1)
            }
            verify(exactly = 1) {
                modeOfPaymentPort.getModeOfPaymentForRevision(requestId, 2)
            }
            verify(exactly = 0) { snapshotPort.getCitizenInformationSnapshot(any(), any()) }
            verify(exactly = 0) { loadCitizenPort.getCitizenWithAddress(any()) }
        }

        @Test
        fun `should throw RequestInvalidStateException when citizen information revision number is null for C1 source`() {
            // Given
            val requestId = UUID.randomUUID()
            val revisionNumbers = WaveTaskRevisionNumbers(
                citizenInformationRevisionNumber = null,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4
            )

            every {
                waveTaskPersistencePort.getCitizenInformationRevision(requestId, "CHANGE_PERSONAL_DATA_CAPTURE")
            } returns revisionNumbers

            // When/Then
            assertThatThrownBy {
                historicalInformationService.getHistoricalCitizenC1(
                    requestId
                )
            }.isInstanceOf(RequestInvalidStateException::class.java)
                .hasMessage("Revision numbers not found for request $requestId and task CHANGE_PERSONAL_DATA_CAPTURE")

            verify(exactly = 1) {
                waveTaskPersistencePort.getCitizenInformationRevision(requestId, "CHANGE_PERSONAL_DATA_CAPTURE")
            }
            verify(exactly = 0) { citizenInformationPort.getCitizenInformationForRevision(any(), any()) }
            verify(exactly = 0) { modeOfPaymentPort.getModeOfPaymentForRevision(any(), any()) }
        }

        @Test
        fun `should throw RequestInvalidStateException when mode of payment revision number is null for C1 source`() {
            // Given
            val requestId = UUID.randomUUID()
            val revisionNumbers = WaveTaskRevisionNumbers(
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = null,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4
            )

            every {
                waveTaskPersistencePort.getCitizenInformationRevision(requestId, "CHANGE_PERSONAL_DATA_CAPTURE")
            } returns revisionNumbers

            // When/Then
            assertThatThrownBy {
                historicalInformationService.getHistoricalCitizenC1(
                    requestId
                )
            }.isInstanceOf(RequestInvalidStateException::class.java)
                .hasMessage("Revision numbers not found for request $requestId and task CHANGE_PERSONAL_DATA_CAPTURE")

            verify(exactly = 1) {
                waveTaskPersistencePort.getCitizenInformationRevision(requestId, "CHANGE_PERSONAL_DATA_CAPTURE")
            }
            verify(exactly = 0) { citizenInformationPort.getCitizenInformationForRevision(any(), any()) }
            verify(exactly = 0) { modeOfPaymentPort.getModeOfPaymentForRevision(any(), any()) }
        }

        @Test
        fun `should throw RequestInvalidStateException when citizen information is not found for revision number for C1 source`() {
            // Given
            val requestId = UUID.randomUUID()
            val revisionNumbers = WaveTaskRevisionNumbers(
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4
            )

            every {
                waveTaskPersistencePort.getCitizenInformationRevision(requestId, "CHANGE_PERSONAL_DATA_CAPTURE")
            } returns revisionNumbers
            every {
                citizenInformationPort.getCitizenInformationForRevision(requestId, 1)
            } returns null

            // When/Then
            assertThatThrownBy {
                historicalInformationService.getHistoricalCitizenC1(
                    requestId
                )
            }.isInstanceOf(RequestInvalidStateException::class.java)
                .hasMessage("Revision number not found for request $requestId and task CHANGE_PERSONAL_DATA_CAPTURE")

            verify(exactly = 1) {
                waveTaskPersistencePort.getCitizenInformationRevision(requestId, "CHANGE_PERSONAL_DATA_CAPTURE")
            }
            verify(exactly = 1) {
                citizenInformationPort.getCitizenInformationForRevision(requestId, 1)
            }
            verify(exactly = 0) { modeOfPaymentPort.getModeOfPaymentForRevision(any(), any()) }
        }
    }

    @Nested
    inner class GetModeOfPaymentTests {

        private val requestId = UUID.randomUUID()
        private val request = BasicRequest(
            id = requestId,
            c9id = 12345L,
            ssin = "123456789",
            opKey = "OP123",
            sectOp = "SO123",
            requestDate = LocalDate.now(),
            decisionType = null,
            decisionBarema = null,
        )

        @Test
        fun `should return mode of payment from C1 source when available`() {
            // Given
            val revisionNumbers = WaveTaskRevisionNumbers(
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4
            )

            val modeOfPayment = ModeOfPayment(
                otherPersonName = null,
                iban = "****************",
                bic = "BBRUBEBB",
            )

            every { requestPort.getRequest(requestId) } returns request
            every {
                waveTaskPersistencePort.getCitizenInformationRevision(requestId, "CHANGE_PERSONAL_DATA_CAPTURE")
            } returns revisionNumbers
            every {
                modeOfPaymentPort.getModeOfPaymentForRevision(requestId, 2)
            } returns modeOfPayment

            // When
            val result = historicalInformationService.getModeOfPayment(requestId, ExternalSource.C1)

            // Then
            assertThat(result).isNotNull
            assertThat(result.otherPersonName).isEqualTo(modeOfPayment.otherPersonName)
            assertThat(result.iban).isEqualTo(modeOfPayment.iban)
            assertThat(result.bic).isEqualTo(modeOfPayment.bic)

            verify(exactly = 0) { requestPort.getRequest(requestId) }
            verify(exactly = 1) {
                waveTaskPersistencePort.getCitizenInformationRevision(requestId, "CHANGE_PERSONAL_DATA_CAPTURE")
            }
            verify(exactly = 1) {
                modeOfPaymentPort.getModeOfPaymentForRevision(requestId, 2)
            }
        }

        @Test
        fun `should throw RequestInvalidStateException when mode of payment revision number is null for C1 source`() {
            // Given
            val revisionNumbers = WaveTaskRevisionNumbers(
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = null,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4
            )

            every {
                waveTaskPersistencePort.getCitizenInformationRevision(requestId, "CHANGE_PERSONAL_DATA_CAPTURE")
            } returns revisionNumbers

            // When/Then
            assertThatThrownBy {
                historicalInformationService.getModeOfPayment(requestId, ExternalSource.C1)
            }.isInstanceOf(RequestInvalidStateException::class.java)
                .hasMessage("Revision numbers not found for request $requestId and task CHANGE_PERSONAL_DATA_CAPTURE")

            verify(exactly = 0) { requestPort.getRequest(requestId) }
            verify(exactly = 1) {
                waveTaskPersistencePort.getCitizenInformationRevision(requestId, "CHANGE_PERSONAL_DATA_CAPTURE")
            }
            verify(exactly = 0) {
                modeOfPaymentPort.getModeOfPaymentForRevision(any(), any())
            }
        }

        @Test
        fun `should throw ExternalSourceNotImplementedException when source is not C1`() {
            // When/Then
            assertThatThrownBy {
                historicalInformationService.getModeOfPayment(requestId, ExternalSource.ONEM)
            }.isInstanceOf(ExternalSourceNotImplementedException::class.java)
                .hasMessage("Mode of payment is not implemented for source ${ExternalSource.ONEM}")

            verify(exactly = 0) { requestPort.getRequest(requestId) }
            verify(exactly = 0) {
                waveTaskPersistencePort.getCitizenInformationRevision(any(), any())
            }
            verify(exactly = 0) {
                modeOfPaymentPort.getModeOfPaymentForRevision(any(), any())
            }
        }
    }

    @Nested
    inner class GetUnionContributionTests {

        private val requestId = UUID.randomUUID()
        private val request = BasicRequest(
            id = requestId,
            c9id = 12345L,
            ssin = "123456789",
            opKey = "OP123",
            sectOp = "SO123",
            requestDate = LocalDate.now(),
            decisionType = null,
            decisionBarema = null,
        )

        @Test
        fun `should return union contribution from C1 source when available`() {
            // Given
            val revisionNumbers = WaveTaskRevisionNumbers(
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4
            )

            val unionContribution = UnionContribution(
                authorized = true, effectiveDate = LocalDate.of(2023, 1, 1)
            )

            every { requestPort.getRequest(requestId) } returns request
            every {
                waveTaskPersistencePort.getCitizenInformationRevision(requestId, "CHANGE_PERSONAL_DATA_CAPTURE")
            } returns revisionNumbers
            every {
                unionContributionPort.getUnionContributionForRevision(requestId, 3)
            } returns unionContribution

            // When
            val result = historicalInformationService.getUnionContribution(requestId, ExternalSource.C1)

            // Then
            assertThat(result).isNotNull
            assertThat(result.authorized).isEqualTo(unionContribution.authorized)
            assertThat(result.effectiveDate).isEqualTo(unionContribution.effectiveDate)

            verify(exactly = 0) { requestPort.getRequest(requestId) }
            verify(exactly = 1) {
                waveTaskPersistencePort.getCitizenInformationRevision(requestId, "CHANGE_PERSONAL_DATA_CAPTURE")
            }
            verify(exactly = 1) {
                unionContributionPort.getUnionContributionForRevision(requestId, 3)
            }
        }

        @Test
        fun `should throw RequestInvalidStateException when union contribution revision number is null for C1 source`() {
            // Given
            val revisionNumbers = WaveTaskRevisionNumbers(
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = null,
                requestInformationRevisionNumber = 4
            )

            every {
                waveTaskPersistencePort.getCitizenInformationRevision(requestId, "CHANGE_PERSONAL_DATA_CAPTURE")
            } returns revisionNumbers

            // When/Then
            assertThatThrownBy {
                historicalInformationService.getUnionContribution(requestId, ExternalSource.C1)
            }.isInstanceOf(RequestInvalidStateException::class.java)
                .hasMessage("Revision numbers not found for request $requestId and task CHANGE_PERSONAL_DATA_CAPTURE")

            verify(exactly = 0) { requestPort.getRequest(requestId) }
            verify(exactly = 1) {
                waveTaskPersistencePort.getCitizenInformationRevision(requestId, "CHANGE_PERSONAL_DATA_CAPTURE")
            }
            verify(exactly = 0) {
                unionContributionPort.getUnionContributionForRevision(any(), any())
            }
        }

        @Test
        fun `should throw RequestInvalidStateException when union contribution is not found for C1 source`() {
            // Given
            val revisionNumbers = WaveTaskRevisionNumbers(
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4
            )

            every {
                waveTaskPersistencePort.getCitizenInformationRevision(requestId, "CHANGE_PERSONAL_DATA_CAPTURE")
            } returns revisionNumbers
            every {
                unionContributionPort.getUnionContributionForRevision(requestId, 3)
            } returns null

            // When/Then
            assertThatThrownBy {
                historicalInformationService.getUnionContribution(requestId, ExternalSource.C1)
            }.isInstanceOf(RequestInvalidStateException::class.java)
                .hasMessage("Revision number not found for request $requestId and task CHANGE_PERSONAL_DATA_CAPTURE")

            verify(exactly = 0) { requestPort.getRequest(requestId) }
            verify(exactly = 1) {
                waveTaskPersistencePort.getCitizenInformationRevision(requestId, "CHANGE_PERSONAL_DATA_CAPTURE")
            }
            verify(exactly = 1) {
                unionContributionPort.getUnionContributionForRevision(requestId, 3)
            }
        }

        @Test
        fun `should throw ExternalSourceNotImplementedException when source is not C1`() {
            // When/Then
            assertThatThrownBy {
                historicalInformationService.getUnionContribution(requestId, ExternalSource.ONEM)
            }.isInstanceOf(ExternalSourceNotImplementedException::class.java)
                .hasMessage("Mode of payment is not implemented for source ${ExternalSource.ONEM}")

            verify(exactly = 0) { requestPort.getRequest(requestId) }
            verify(exactly = 0) {
                waveTaskPersistencePort.getCitizenInformationRevision(any(), any())
            }
            verify(exactly = 0) {
                unionContributionPort.getUnionContributionForRevision(any(), any())
            }
        }
    }

    @Nested
    inner class GetBaremaTests {

        private val requestId = UUID.randomUUID()
        private val ssin = "12345678901"
        private val numbox = 42
        private val requestDate = LocalDate.of(2025, 3, 15)
        private val expectedBarema = Barema("01-TEST", "ARTICLE-123")

        @Test
        fun `should return barema from readonly snapshot if available`() {
            // Given
            val snapshot = Snapshot.Found(expectedBarema, true)

            every { snapshotPort.getBaremaSnapshot(requestId) } returns snapshot

            // When
            val result = historicalInformationService.getBarema(requestId)

            // Then
            assertThat(result).isEqualTo(expectedBarema)

            // Verify only the snapshot was checked, no other operations
            verify(exactly = 1) { snapshotPort.getBaremaSnapshot(requestId) }
            verify(exactly = 0) { requestPort.getRequest(any()) }
            verify(exactly = 0) { loadCitizenPort.getCitizenNumbox(any()) }
            verify(exactly = 0) { baremaPort.getLatestBarema(any(), any()) }
            verify(exactly = 0) { snapshotPort.saveBaremaSnapshot(any(), any()) }
        }

        @Test
        fun `should fetch and save barema when snapshot is not readonly`() {
            // Given
            val snapshot = Snapshot.Found(expectedBarema, false)
            val changePersonalDataRequest = ChangePersonalDataRequest(
                id = requestId,
                c9id = 12345L,
                ssin = ssin,
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = LocalDate.now(),
                documentType = IdentityDocumentType.ELECTRONIC,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                requestInformation = RequestInformation(requestDate),
                changePersonalDataCaptureWaveTask = null,
                changePersonalDataValidateWaveTask = null,
                decisionType = null,
                decisionBarema = null,
            )

            every { snapshotPort.getBaremaSnapshot(requestId) } returns snapshot
            every { requestPort.getRequest(requestId) } returns changePersonalDataRequest
            every { loadCitizenPort.getCitizenNumbox(ssin) } returns numbox
            every { baremaPort.getLatestBarema(numbox, requestDate) } returns expectedBarema
            every { snapshotPort.saveBaremaSnapshot(requestId, expectedBarema) } returns Unit

            // When
            val result = historicalInformationService.getBarema(requestId)

            // Then
            assertThat(result).isEqualTo(expectedBarema)

            verifySequence {
                snapshotPort.getBaremaSnapshot(requestId)
                requestPort.getRequest(requestId)
                loadCitizenPort.getCitizenNumbox(ssin)
                baremaPort.getLatestBarema(numbox, requestDate)
                snapshotPort.saveBaremaSnapshot(requestId, expectedBarema)
            }
        }

        @Test
        fun `should fetch and save barema when no snapshot exists`() {
            // Given
            val changePersonalDataRequest = ChangePersonalDataRequest(
                id = requestId,
                c9id = 12345L,
                ssin = ssin,
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = LocalDate.now(),
                documentType = IdentityDocumentType.ELECTRONIC,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                requestInformation = RequestInformation(requestDate),
                changePersonalDataCaptureWaveTask = null,
                changePersonalDataValidateWaveTask = null,
                decisionType = null,
                decisionBarema = null,
            )

            every { snapshotPort.getBaremaSnapshot(requestId) } returns Snapshot.NotFound
            every { requestPort.getRequest(requestId) } returns changePersonalDataRequest
            every { loadCitizenPort.getCitizenNumbox(ssin) } returns numbox
            every { baremaPort.getLatestBarema(numbox, requestDate) } returns expectedBarema
            every { snapshotPort.saveBaremaSnapshot(requestId, expectedBarema) } returns Unit

            // When
            val result = historicalInformationService.getBarema(requestId)

            // Then
            assertThat(result).isEqualTo(expectedBarema)

            verifySequence {
                snapshotPort.getBaremaSnapshot(requestId)
                requestPort.getRequest(requestId)
                loadCitizenPort.getCitizenNumbox(ssin)
                baremaPort.getLatestBarema(numbox, requestDate)
                snapshotPort.saveBaremaSnapshot(requestId, expectedBarema)
            }
        }

        @Test
        fun `should use fallback date from request when requestInformation is null`() {
            // Given
            val fallbackDate = LocalDate.of(2025, 1, 10)
            val changePersonalDataRequest = ChangePersonalDataRequest(
                id = requestId,
                c9id = 12345L,
                ssin = ssin,
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = fallbackDate,
                documentType = IdentityDocumentType.ELECTRONIC,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                requestInformation = null,
                changePersonalDataCaptureWaveTask = null,
                changePersonalDataValidateWaveTask = null,
                decisionType = null,
                decisionBarema = null,
            )

            every { snapshotPort.getBaremaSnapshot(requestId) } returns Snapshot.NotFound
            every { requestPort.getRequest(requestId) } returns changePersonalDataRequest
            every { loadCitizenPort.getCitizenNumbox(ssin) } returns numbox
            every { baremaPort.getLatestBarema(numbox, fallbackDate) } returns expectedBarema
            every { snapshotPort.saveBaremaSnapshot(requestId, expectedBarema) } returns Unit

            // When
            val result = historicalInformationService.getBarema(requestId)

            // Then
            assertThat(result).isEqualTo(expectedBarema)

            verify { baremaPort.getLatestBarema(numbox, fallbackDate) }
        }
    }

    @Nested
    inner class GetHistoricalCitizenAuthenticSourcesTests {

        private val requestId = UUID.randomUUID()
        private val ssin = "123456789"
        private val request = BasicRequest(
            id = requestId,
            c9id = 12345L,
            ssin = ssin,
            opKey = "OP123",
            sectOp = "SO123",
            requestDate = LocalDate.now(),
            decisionType = null,
            decisionBarema = null,
        )

        private val historicalCitizenAuthenticSources = be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenAuthenticSources(
            firstName = "John",
            lastName = "Doe",
            nationality = "BE",
            address = AddressNullable(
                street = "Main Street",
                houseNumber = "42",
                boxNumber = "A",
                zipCode = "1000",
                city = "Brussels",
                country = "BE",
                valueDate = LocalDate.of(2022, 1, 1)
            ),
            birthDate = LocalDate.of(1990, 1, 1)
        )

        private val historicalCitizenSnapshot = HistoricalCitizenSnapshot(
            firstName = "John",
            lastName = "Doe",
            numbox = null,
            nationality = "BE",
            birthDate = LocalDate.of(1990, 1, 1),
            address = AddressNullable(
                street = "Main Street",
                houseNumber = "42",
                boxNumber = "A",
                zipCode = "1000",
                city = "Brussels",
                country = "BE",
                valueDate = LocalDate.of(2022, 1, 1)
            ),
            iban = null,
            bic = null,
            otherPersonName = null,
            bankAccountValueDate = null,
            paymentMode = null,
            authorized = null,
            effectiveDate = null
        )

        @Test
        fun `should return citizen information from readonly snapshot when available`() {
            // Given
            val snapshot = Snapshot.Found(historicalCitizenSnapshot, readonly = true)

            every { requestPort.getRequest(requestId) } returns request
            every { snapshotPort.getCitizenInformationSnapshot(requestId, ExternalSource.AUTHENTIC_SOURCES) } returns snapshot

            // When
            val result = historicalInformationService.getHistoricalCitizenAuthenticSources(requestId)

            // Then
            assertThat(result).isEqualTo(historicalCitizenAuthenticSources)

            verify(exactly = 1) { requestPort.getRequest(requestId) }
            verify(exactly = 1) { snapshotPort.getCitizenInformationSnapshot(requestId, ExternalSource.AUTHENTIC_SOURCES) }
            verify(exactly = 0) { registryPort.getRegistryInformationForCitizen(any()) }
            verify(exactly = 0) { snapshotPort.saveCitizenInformationSnapshot(any(), any(), any()) }
        }

        @Test
        fun `should fetch and save citizen information when snapshot is not readonly`() {
            // Given
            val snapshot = Snapshot.Found(historicalCitizenSnapshot, readonly = false)

            every { requestPort.getRequest(requestId) } returns request
            every { snapshotPort.getCitizenInformationSnapshot(requestId, ExternalSource.AUTHENTIC_SOURCES) } returns snapshot
            every { registryPort.getRegistryInformationForCitizen(ssin) } returns historicalCitizenAuthenticSources
            every { snapshotPort.saveCitizenInformationSnapshot(requestId, ExternalSource.AUTHENTIC_SOURCES, historicalCitizenSnapshot) } returns Unit

            // When
            val result = historicalInformationService.getHistoricalCitizenAuthenticSources(requestId)

            // Then
            assertThat(result).isEqualTo(historicalCitizenAuthenticSources)

            verifySequence {
                requestPort.getRequest(requestId)
                snapshotPort.getCitizenInformationSnapshot(requestId, ExternalSource.AUTHENTIC_SOURCES)
                registryPort.getRegistryInformationForCitizen(ssin)
                snapshotPort.saveCitizenInformationSnapshot(requestId, ExternalSource.AUTHENTIC_SOURCES, historicalCitizenSnapshot)
            }
        }

        @Test
        fun `should fetch and save citizen information when no snapshot exists`() {
            // Given
            every { requestPort.getRequest(requestId) } returns request
            every { snapshotPort.getCitizenInformationSnapshot(requestId, ExternalSource.AUTHENTIC_SOURCES) } returns Snapshot.NotFound
            every { registryPort.getRegistryInformationForCitizen(ssin) } returns historicalCitizenAuthenticSources
            every { snapshotPort.saveCitizenInformationSnapshot(requestId, ExternalSource.AUTHENTIC_SOURCES, historicalCitizenSnapshot) } returns Unit

            // When
            val result = historicalInformationService.getHistoricalCitizenAuthenticSources(requestId)

            // Then
            assertThat(result).isEqualTo(historicalCitizenAuthenticSources)

            verifySequence {
                requestPort.getRequest(requestId)
                snapshotPort.getCitizenInformationSnapshot(requestId, ExternalSource.AUTHENTIC_SOURCES)
                registryPort.getRegistryInformationForCitizen(ssin)
                snapshotPort.saveCitizenInformationSnapshot(requestId, ExternalSource.AUTHENTIC_SOURCES, historicalCitizenSnapshot)
            }
        }

        @Test
        fun `should use different request types correctly`() {
            // Given
            val changePersonalDataRequest = ChangePersonalDataRequest(
                id = requestId,
                c9id = 12345L,
                ssin = ssin,
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = LocalDate.now(),
                documentType = IdentityDocumentType.ELECTRONIC,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                requestInformation = null,
                changePersonalDataCaptureWaveTask = null,
                changePersonalDataValidateWaveTask = null,
                decisionType = null,
                decisionBarema = null,
            )

            every { requestPort.getRequest(requestId) } returns changePersonalDataRequest
            every { snapshotPort.getCitizenInformationSnapshot(requestId, ExternalSource.AUTHENTIC_SOURCES) } returns Snapshot.NotFound
            every { registryPort.getRegistryInformationForCitizen(ssin) } returns historicalCitizenAuthenticSources
            every { snapshotPort.saveCitizenInformationSnapshot(requestId, ExternalSource.AUTHENTIC_SOURCES, historicalCitizenSnapshot) } returns Unit

            // When
            val result = historicalInformationService.getHistoricalCitizenAuthenticSources(requestId)

            // Then
            assertThat(result).isEqualTo(historicalCitizenAuthenticSources)

            verify(exactly = 1) { requestPort.getRequest(requestId) }
            verify(exactly = 1) { registryPort.getRegistryInformationForCitizen(ssin) }
        }
    }
}
