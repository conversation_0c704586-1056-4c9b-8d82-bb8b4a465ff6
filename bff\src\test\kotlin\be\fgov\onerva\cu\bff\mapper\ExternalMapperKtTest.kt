package be.fgov.onerva.cu.bff.mapper

import be.fgov.onerva.cu.bff.rest.client.citizen.model.BankAccountDTO
import be.fgov.onerva.cu.bff.rest.client.citizen.model.CitizenInfoDTO
import be.fgov.onerva.cu.common.utils.parseCitizenAddress
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.*
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode
import java.math.BigDecimal

@Execution(ExecutionMode.SAME_THREAD)
class CitizenInfoMapperTest {

    @BeforeEach
    fun setUp() {
        mockkStatic(::parseCitizenAddress)
    }

    @AfterEach
    fun tearDown() {
        unmockkAll()
    }

    @Nested
    @DisplayName("toCitizenInfoWithAddress")
    inner class ToCitizenInfoWithAddress {

        @Test
        @DisplayName("should map DTO with complete address correctly")
        fun shouldMapDtoWithCompleteAddressCorrectly() {
            // Given
            val address = "Rue de la Loi 16"
            val parsedAddress = Triple("Rue de la Loi", "16", null)

            val dto = CitizenInfoDTO().apply {
                this.firstName = "John"
                this.lastName = "Doe"
                this.numBox = BigDecimal.valueOf(42)
                this.flagNation = BigDecimal.valueOf(100)
                this.iban = "****************"
                this.address = address
                this.postalCode = "1000"
                this.paymentMode = 1
                this.bankAccount = BankAccountDTO().apply {
                    this.iban = "****************"
                    this.bic = "THEBIC"
                    this.holder = "The Holder"
                }
            }

            every { parseCitizenAddress(address) } returns parsedAddress

            // When
            val result = dto.toCitizenInfoWithAddress()

            // Then
            assertThat(result).isNotNull
            assertThat(result.firstName).isEqualTo("John")
            assertThat(result.lastName).isEqualTo("Doe")
            assertThat(result.numbox).isEqualTo(42)
            assertThat(result.nationality).isEqualTo("100")
            assertThat(result.iban).isEqualTo("****************")

            assertThat(result.address).isNotNull
            assertThat(result.address.street).isEqualTo("Rue de la Loi")
            assertThat(result.address.houseNumber).isEqualTo("16")
            assertThat(result.address.boxNumber).isNull()
            assertThat(result.address.country).isNull()
            assertThat(result.address.zipCode).isEqualTo("1000")
            assertThat(result.address.city).isNull()
        }

        @Test
        @DisplayName("should map DTO with complete address and box number correctly")
        fun shouldMapDtoWithCompleteAddressAndBoxNumberCorrectly() {
            // Given
            val address = "Avenue Louise 149/B24"
            val parsedAddress = Triple("Avenue Louise", "149", "B24")

            val dto = CitizenInfoDTO().apply {
                this.firstName = "Jane"
                this.lastName = "Smith"
                this.numBox = BigDecimal.valueOf(42)
                this.flagNation = BigDecimal.valueOf(100)
                this.iban = "****************"
                this.address = address
                this.postalCode = "1050"
                this.paymentMode = 1
                this.bankAccount = BankAccountDTO().apply {
                    this.iban = "****************"
                    this.bic = "THEBIC"
                    this.holder = "The Holder"
                }
            }

            every { parseCitizenAddress(address) } returns parsedAddress

            // When
            val result = dto.toCitizenInfoWithAddress()

            // Then
            assertThat(result).isNotNull
            assertThat(result.firstName).isEqualTo("Jane")
            assertThat(result.lastName).isEqualTo("Smith")
            assertThat(result.numbox).isEqualTo(42)
            assertThat(result.nationality).isEqualTo("100")
            assertThat(result.iban).isEqualTo("****************")

            assertThat(result.address).isNotNull
            assertThat(result.address.street).isEqualTo("Avenue Louise")
            assertThat(result.address.houseNumber).isEqualTo("149")
            assertThat(result.address.boxNumber).isEqualTo("B24")
            assertThat(result.address.country).isNull()
            assertThat(result.address.zipCode).isEqualTo("1050")
            assertThat(result.address.city).isNull()
        }

        @Test
        @DisplayName("should handle null address correctly")
        fun shouldHandleNullAddressCorrectly() {
            // Given
            val dto = CitizenInfoDTO().apply {
                this.firstName = "Alice"
                this.lastName = "Johnson"
                this.numBox = null
                this.flagNation = null
                this.address = null
                this.postalCode = "2000"
                this.paymentMode = 1
                this.bankAccount = BankAccountDTO().apply {
                    this.iban = "****************"
                    this.bic = "THEBIC"
                    this.holder = "The Holder"
                }
            }

            // When
            val result = dto.toCitizenInfoWithAddress()

            // Then
            assertThat(result).isNotNull
            assertThat(result.firstName).isEqualTo("Alice")
            assertThat(result.lastName).isEqualTo("Johnson")
            assertThat(result.numbox).isEqualTo(0)
            assertThat(result.nationality).isNull()
            assertThat(result.iban).isEqualTo("****************")

            assertThat(result.address).isNotNull
            assertThat(result.address.street).isNull()
            assertThat(result.address.houseNumber).isNull()
            assertThat(result.address.boxNumber).isNull()
            assertThat(result.address.country).isNull()
            assertThat(result.address.zipCode).isEqualTo("2000")
            assertThat(result.address.city).isNull()
        }
    }
}