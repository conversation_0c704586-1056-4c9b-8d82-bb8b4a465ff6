package be.fgov.onerva.cu.backend.adapter.`in`.controller

import jakarta.validation.ConstraintViolation
import jakarta.validation.ConstraintViolationException
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.RestControllerAdvice
import org.springframework.web.context.request.WebRequest
import org.springframework.web.reactive.result.method.annotation.ResponseEntityExceptionHandler
import be.fgov.onerva.cu.backend.application.exception.BaremaNotFoundException
import be.fgov.onerva.cu.backend.application.exception.CitizenNotFoundException
import be.fgov.onerva.cu.backend.application.exception.InformationNotFoundException
import be.fgov.onerva.cu.backend.application.exception.InvalidInputException
import be.fgov.onerva.cu.backend.application.exception.InvalidRequestIdException
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.exception.RequestInvalidStateException
import be.fgov.onerva.cu.backend.application.exception.WaveException
import be.fgov.onerva.cu.backend.application.exception.WaveTaskNotFoundException
import be.fgov.onerva.cu.common.utils.logger as thisLogger

/**
 * Global exception handler for the CU application.
 *
 * This class provides centralized exception handling across all REST endpoints.
 * It converts various application exceptions into appropriate HTTP responses with
 * structured problem details following RFC 7807.
 */
@RestControllerAdvice
class GlobalExceptionHandler : ResponseEntityExceptionHandler() {
    private val log = thisLogger

    /**
     * Handles internal server errors.
     *
     * @param ex The caught Exception
     * @param request The web request in which the exception occurred
     * @return ResponseEntity containing problem details with INTERNAL_SERVER_ERROR status
     */
    @ExceptionHandler(WaveException::class)
    fun handleInternalServerError(ex: WaveException, request: WebRequest): ResponseEntity<ProblemDetail> {
        log.error("Wave exception occurred: ", ex)
        val problemDetail = ProblemDetail.forStatusAndDetail(
            HttpStatus.INTERNAL_SERVER_ERROR,
            ex.message
        ).apply {
            title = "Wave Exception"
        }
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(problemDetail)
    }

    /**
     * Handles invalid input exceptions.
     *
     * @param ex The caught InvalidInputException
     * @param request The web request in which the exception occurred
     * @return ResponseEntity containing problem details with BAD_REQUEST status
     */
    @ExceptionHandler(InvalidInputException::class)
    fun handleInvalidInputException(ex: InvalidInputException, request: WebRequest): ResponseEntity<ProblemDetail> {
        log.error("Invalid input error: ", ex)
        val problemDetail = ProblemDetail.forStatusAndDetail(
            HttpStatus.BAD_REQUEST,
            ex.message
        ).apply {
            title = "Invalid Input"
        }
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(problemDetail)
    }

    /**
     * Handles requests in invalid state exceptions.
     *
     * @param ex The caught RequestInvalidStateException
     * @return ResponseEntity containing problem details with BAD_REQUEST status
     */
    @ExceptionHandler(RequestInvalidStateException::class)
    fun handleRequestInvalidStateException(
        ex: RequestInvalidStateException,
    ): ResponseEntity<ProblemDetail> {
        log.error("Request is in invalid state: ", ex)
        val problemDetail = ProblemDetail.forStatusAndDetail(
            HttpStatus.BAD_REQUEST,
            ex.message
        ).apply {
            title = "Request is in invalid state"
        }
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(problemDetail)
    }

    /**
     * Handles request ID not found exceptions.
     *
     * @param ex The caught RequestIdNotFoundException
     * @param request The web request in which the exception occurred
     * @return ResponseEntity containing problem details with NOT_FOUND status
     */
    @ExceptionHandler(RequestIdNotFoundException::class)
    fun handleRequestIdNotFoundException(
        ex: RequestIdNotFoundException,
        request: WebRequest,
    ): ResponseEntity<ProblemDetail> {
        log.error("Request ID not found: ", ex)
        val problemDetail = ProblemDetail.forStatusAndDetail(
            HttpStatus.NOT_FOUND,
            ex.message
        ).apply {
            title = "Request Not Found"
        }
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
            .body(problemDetail)
    }

    /**
     * Handles request ID not found exceptions.
     *
     * @param ex The caught CitizenNotFoundException
     * @param request The web request in which the exception occurred
     * @return ResponseEntity containing problem details with NOT_FOUND status
     */
    @ExceptionHandler(CitizenNotFoundException::class)
    fun handleCitizenNotFoundException(
        ex: CitizenNotFoundException,
        request: WebRequest,
    ): ResponseEntity<ProblemDetail> {
        log.error("Request ID not found: ", ex)
        val problemDetail = ProblemDetail.forStatusAndDetail(
            HttpStatus.NOT_FOUND,
            ex.message
        ).apply {
            title = "Request Not Found"
        }
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
            .body(problemDetail)
    }

    /**
     * Handles request ID not found exceptions.
     *
     * @param ex The caught BaremaNotFoundException
     * @param request The web request in which the exception occurred
     * @return ResponseEntity containing problem details with NOT_FOUND status
     */
    @ExceptionHandler(BaremaNotFoundException::class)
    fun handleBaremaNotFoundException(
        ex: BaremaNotFoundException,
        request: WebRequest,
    ): ResponseEntity<ProblemDetail> {
        log.info("Barema ID not found: ", ex)
        val problemDetail = ProblemDetail.forStatusAndDetail(
            HttpStatus.NOT_FOUND,
            ex.message
        ).apply {
            title = "Barema Not Found"
        }
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
            .body(problemDetail)
    }

    /**
     * Handles invalid request ID exceptions.
     * It happens when a request is made for a resource corresponding to a request id but the request id does not
     * exist.
     */
    @ExceptionHandler(InvalidRequestIdException::class)
    fun handleInvalidRequestIdException(
        ex: InvalidRequestIdException,
        request: WebRequest,
    ): ResponseEntity<ProblemDetail> {
        log.error("Invalid request ID: ", ex)
        val problemDetail = ProblemDetail.forStatusAndDetail(
            HttpStatus.BAD_REQUEST,
            ex.message
        ).apply {
            title = "Invalid Request ID"
        }
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(problemDetail)
    }

    /**
     * Handles information not found exceptions.
     *
     * @param ex The caught InformationNotFoundException
     * @param request The web request in which the exception occurred
     * @return ResponseEntity containing problem details with NOT_FOUND status
     */
    @ExceptionHandler(InformationNotFoundException::class)
    fun handleInformationNotFoundException(
        ex: InformationNotFoundException,
        request: WebRequest,
    ): ResponseEntity<ProblemDetail> {
        log.info("Information not found: {}", ex.message)
        val problemDetail = ProblemDetail.forStatusAndDetail(
            HttpStatus.NOT_FOUND,
            ex.message
        ).apply {
            title = "Information Not Found"
        }
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
            .body(problemDetail)
    }

    /**
     * Handles constraint violation exceptions from validation.
     *
     * Processes validation failures and returns a structured response containing
     * all validation errors. The response includes a map of field names to error messages.
     *
     * @param ex The caught ConstraintViolationException
     * @return ResponseEntity containing problem details with BAD_REQUEST status and validation errors
     */
    @ExceptionHandler(ConstraintViolationException::class)
    fun handleValidationExceptions(
        ex: ConstraintViolationException,
    ): ResponseEntity<ProblemDetail> {
        log.warn("Validation failed: ", ex)
        val problemDetail = ProblemDetail.forStatusAndDetail(
            HttpStatus.BAD_REQUEST,
            "Validation failed"
        )

        val errors: MutableMap<String, String> = HashMap()
        ex.constraintViolations.forEach {
            val fieldName = (it as ConstraintViolation<*>).propertyPath.toString()
            val errorMessage: String = it.message
            errors.put(fieldName, errorMessage)
        }

        problemDetail.setProperty("errors", errors)
        return ResponseEntity.badRequest().body(problemDetail)
    }

    @ExceptionHandler(WaveTaskNotFoundException::class)
    fun handleWaveTaskNotFoundException(
        ex: WaveTaskNotFoundException,
        request: WebRequest,
    ): ResponseEntity<ProblemDetail> {
        log.error("Wave task not found: ", ex)
        val problemDetail = ProblemDetail.forStatusAndDetail(
            HttpStatus.NOT_FOUND,
            ex.message
        ).apply {
            title = "Wave Task Not Found"
        }
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
            .body(problemDetail)
    }
}