<div class="onemrva-theme">
    <lib-loading-component></lib-loading-component>

    <div *ngIf="cdfForm()">
        <lib-cu-closed-or-treated-on-main-frame
                [status]="status" [decisionType]="citizenData()?.decisionType"
                [task]="task"
                [decisionBarema]="citizenData()?.decisionBarema"
                [nextTaskDescription]="'CU_DATA_CAPTURE.NEXT_TASK.DESCRIPTION'"
                [nextTaskAction]="'CU_DATA_CAPTURE.NEXT_TASK.ACTION'"
                [pushbackStatus]="citizenData()?.pushbackStatus">
        </lib-cu-closed-or-treated-on-main-frame>

        <lib-cu-c9-annexes [language]="language" [annexes]="citizenData()?.annexes">
        </lib-cu-c9-annexes>

        <lib-cu-cdf
                [requestBasicInfoResponse]="citizenData()!"
                [citizenInformation]="citizenInformation()!"
                [paymentData]="paymentData()!"
                [unionData]="unionData()!"
                [language]="language"
                [taskStatus]="status"
                [task]="task"
                [cdfForm]="cdfForm()"
        ></lib-cu-cdf>

        <div class="actions">
            <button mat-button
                    id="saveAsDraftButton"
                    [hidden]="isFormClosedOrWaiting()"
                    (click)="save()"
                    [disabled]="cdfForm().invalid"
                    color="primary"
                    aria-label="Basic"
                    data-cy="saveAsDraftRequestButton"
            >{{ 'CU_DATA_CONSISTENCY.BUTTONS.SAVE_AS_DRAFT' | translate }}
            </button>

            <button mat-stroked-button
                    id="sendC51Button"
                    [hidden]="isFormClosedOrWaiting()"
                    (click)="sendC51()"
                    [disabled]="cdfForm().invalid"
                    color="primary"
                    aria-label="Basic"
                    data-cy="sendC51RequestButton"
            >{{ 'CU_DATA_CONSISTENCY.BUTTONS.SEND_C51' | translate }}
            </button>

            <button mat-flat-button
                    id="sendButton"
                    [hidden]="isFormClosedOrWaiting()"
                    (click)="save(true)"
                    color="accent"
                    aria-label="Basic"
                    [disabled]="cdfForm().invalid"
                    data-cy="validateRequestButton">
                {{ 'CU_DATA_CAPTURE.BUTTONS.VALIDATE' | translate }}
            </button>
        </div>
    </div>
</div>
