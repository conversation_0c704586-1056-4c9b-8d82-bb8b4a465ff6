package be.fgov.onerva.cu.backend.adapter.out.external.citizen

import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.application.domain.Address
import be.fgov.onerva.cu.backend.application.domain.UpdateCitizen
import be.fgov.onerva.cu.backend.application.exception.CitizenNotFoundException
import be.fgov.onerva.cu.backend.application.exception.InvalidExternalDataException
import be.fgov.onerva.person.api.CitizenApi
import be.fgov.onerva.person.api.CitizenInfoApi
import be.fgov.onerva.person.rest.model.BankAccountDTO
import be.fgov.onerva.person.rest.model.CitizenDTO
import be.fgov.onerva.person.rest.model.CitizenInfoDTO
import be.fgov.onerva.person.rest.model.CitizenInfoPageDTO
import be.fgov.onerva.person.rest.model.CitizenInfoUnionDueDTO
import be.fgov.onerva.person.rest.model.CitizenUpdateRequestDTO
import be.fgov.onerva.person.rest.model.ForeignAddressDTO
import be.fgov.onerva.person.rest.model.PaymentTypeDTO
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.slot
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class CitizenAdapterTest {

    @MockK
    private lateinit var citizenApi: CitizenApi

    @MockK
    private lateinit var citizenInfoApi: CitizenInfoApi

    @InjectMockKs
    private lateinit var citizenAdapter: CitizenAdapter

    @Test
    fun `getCitizenNumbox should return numbox when citizen exists`() {
        // Given
        val ssin = "***********"
        val expectedNumbox = 42
        val citizenDTO = CitizenDTO().apply {
            numbox = expectedNumbox
        }

        every { citizenApi.getByNiss(ssin) } returns citizenDTO

        // When
        val result = citizenAdapter.getCitizenNumbox(ssin)

        // Then
        assertThat(result).isEqualTo(expectedNumbox)
        verify(exactly = 1) { citizenApi.getByNiss(ssin) }
    }

    @Test
    fun `getCitizenNumbox should throw CitizenNotFoundException when citizen not found`() {
        // Given
        val ssin = "***********"
        every { citizenApi.getByNiss(ssin) } returns null

        // When/Then
        assertThatThrownBy { citizenAdapter.getCitizenNumbox(ssin) }
            .isInstanceOf(CitizenNotFoundException::class.java)
            .hasMessage("Citizen is not found: $ssin")

        verify(exactly = 1) { citizenApi.getByNiss(ssin) }
    }

    @Test
    fun `getCitizenBySsin should return citizen when exists`() {
        // Given
        val ssin = "***********"
        val citizenDTO = CitizenDTO().apply {
            firstname = "John"
            lastname = "Doe"
            numbox = 42
            zipCode = 1000
        }

        every { citizenApi.getByNiss(ssin) } returns citizenDTO

        // When
        val result = citizenAdapter.getCitizenBySsin(ssin)

        // Then
        assertThat(result).isNotNull()
            .extracting("firstName", "lastName", "numbox", "zipCode")
            .containsExactly("John", "Doe", 42, "1000")

        verify(exactly = 1) { citizenApi.getByNiss(ssin) }
    }

    @Test
    fun `getCitizenBySsin should throw CitizenNotFoundException when citizen not found`() {
        // Given
        val ssin = "***********"
        every { citizenApi.getByNiss(ssin) } returns null

        // When/Then
        assertThatThrownBy { citizenAdapter.getCitizenBySsin(ssin) }
            .isInstanceOf(CitizenNotFoundException::class.java)
            .hasMessage("Citizen is not found: $ssin")

        verify(exactly = 1) { citizenApi.getByNiss(ssin) }
    }

    @Test
    fun `getCitizenBySsin should handle null zipCode`() {
        // Given
        val ssin = "***********"
        val citizenDTO = CitizenDTO().apply {
            firstname = "John"
            lastname = "Doe"
            numbox = 42
            zipCode = null
        }

        every { citizenApi.getByNiss(ssin) } returns citizenDTO

        // When
        val result = citizenAdapter.getCitizenBySsin(ssin)

        // Then
        assertThat(result).isNotNull()
            .extracting("firstName", "lastName", "numbox", "zipCode")
            .containsExactly("John", "Doe", 42, "")

        verify(exactly = 1) { citizenApi.getByNiss(ssin) }
    }

    @Test
    fun `getCitizenWithAddress should return mapped citizen info when citizen exists`() {
        // Given
        val ssin = "***********"
        val citizenInfoPageDTO = CitizenInfoPageDTO().apply {
            content = listOf(
                CitizenInfoDTO().also {
                    it.firstName = "John"
                    it.lastName = "Doe"
                    it.numBox = BigDecimal(42)
                    it.flagNation = BigDecimal(150)
                    it.address = "Main Street 123 Box A"
                    it.postalCode = "1000"
                    it.paymentMode = 2
                    it.bankAccount = BankAccountDTO().also {
                        it.iban = "****************"
                        it.bic = "THEBIC"
                        it.holder = "The Holder"
                    }
                    it.unionDue = CitizenInfoUnionDueDTO().also {
                        it.mandateActive = false
                        it.validFrom = null
                    }
                    it.addressObj = ForeignAddressDTO().also {
                        it.city = "Brussels"
                        it.street = "Main Street"
                        it.box = "Box A"
                        it.countryCode = 150
                        it.zip = "1000"
                        it.number = "123"
                    }
                }
            )
            pageNumber = 0
            pageSize = 10
            totalElements = 1
            isFirst = true
            isLast = true
        }

        every {
            citizenInfoApi.searchCitizenInfo(
                listOf(ssin),
                null,
                "SUMMARY",
                0,
                10
            )
        } returns citizenInfoPageDTO

        // When
        val result = citizenAdapter.getCitizenWithAddress(ssin)

        // Then
        assertThat(result).isNotNull()
        requireNotNull(result)
        assertThat(result.firstName).isEqualTo("John")
        assertThat(result.lastName).isEqualTo("Doe")
        assertThat(result.numbox).isEqualTo(42)
        assertThat(result.nationality).isEqualTo("150")
        assertThat(result.iban).isEqualTo("****************")
        assertThat(result.bic).isEqualTo("THEBIC")
        assertThat(result.otherPersonName).isEqualTo("The Holder")
        assertThat(result.address).isNotNull()
        requireNotNull(result.address)
        assertThat(result.address.street).isEqualTo("Main Street")
        assertThat(result.address.houseNumber).isEqualTo("123")
        assertThat(result.address.boxNumber).isEqualTo("Box A")
        assertThat(result.address.zipCode).isEqualTo("1000")
        assertThat(result.authorized).isEqualTo(false)
        assertThat(result.effectiveDate).isNull()

        verify(exactly = 1) { citizenInfoApi.searchCitizenInfo(listOf(ssin), null, "SUMMARY", 0, 10) }
    }

    @Test
    fun `getCitizenWithAddress should return null when citizen not found`() {
        // Given
        val ssin = "***********"
        val emptyPageResponse = CitizenInfoPageDTO().apply {
            content = emptyList()
            pageNumber = 0
            pageSize = 10
            totalElements = 0
            isFirst = true
            isLast = true
        }

        every {
            citizenInfoApi.searchCitizenInfo(
                listOf(ssin),
                null,
                "SUMMARY",
                0,
                10
            )
        } returns emptyPageResponse

        // When
        val result = citizenAdapter.getCitizenWithAddress(ssin)

        // Then
        assertThat(result).isNull()
        verify(exactly = 1) { citizenInfoApi.searchCitizenInfo(listOf(ssin), null, "SUMMARY", 0, 10) }
    }

    @Test
    fun `getCitizenWithAddress should handle null address fields`() {
        // Given
        val ssin = "***********"
        val citizenInfoPageDTO = CitizenInfoPageDTO().apply {
            content = listOf(
                CitizenInfoDTO().apply {
                    firstName = "John"
                    lastName = "Doe"
                    numBox = BigDecimal(42)
                    flagNation = BigDecimal(150)
                    iban = "****************"
                    address = null
                    postalCode = null
                }
            )
            pageNumber = 0
            pageSize = 10
            totalElements = 1
            isFirst = true
            isLast = true
        }

        every {
            citizenInfoApi.searchCitizenInfo(
                listOf(ssin),
                null,
                "SUMMARY",
                0,
                10
            )
        } returns citizenInfoPageDTO

        // When / Then
        assertThatThrownBy {
            citizenAdapter.getCitizenWithAddress(ssin)
        }
            .isInstanceOf(InvalidExternalDataException::class.java)
            .hasMessage("Invalid address format: null")
    }

    @Test
    fun `updateCitizenInformation should call citizenApi with correct parameters`() {
        // Given
        val requestId = UUID.randomUUID()
        val ssin = "85050599890"
        val userName = "test_user"
        val street = "Main Street"
        val houseNumber = "123"
        val boxNumber = "A"
        val zipCode = "1000"
        val city = "Brussels"
        val nationality = "150"
        val authorized = true
        val valueDate = LocalDate.of(2023, 5, 15)

        val updateCitizen = UpdateCitizen(
            ssin = ssin,
            birthDate = LocalDate.of(1989, 7, 7),
            userName = userName,
            address = Address(
                street = street,
                houseNumber = houseNumber,
                boxNumber = boxNumber,
                zipCode = zipCode,
                city = city,
                country = "150"
            ),
            nationality = nationality,
            authorized = authorized,
            valueDate = valueDate,
            correlationId = requestId.toString(),
        )

        val requestDtoSlot = slot<CitizenUpdateRequestDTO>()

        every {
            citizenApi.updateCitizen(eq(ssin), eq(userName), capture(requestDtoSlot))
        } returns Unit

        // When
        citizenAdapter.updateCitizenInformation(requestId, updateCitizen)

        // Then
        verify(exactly = 1) { citizenApi.updateCitizen(eq(ssin), eq(userName), any()) }

        val capturedRequest = requestDtoSlot.captured
        assertThat(capturedRequest.address).isNotNull
        assertThat(capturedRequest.address.street).isEqualTo(street)
        assertThat(capturedRequest.address.number).isEqualTo(houseNumber)
        assertThat(capturedRequest.address.box).isEqualTo(boxNumber)
        assertThat(capturedRequest.address.zip).isEqualTo(1000)
        assertThat(capturedRequest.address.city).isEqualTo(city)
        assertThat(capturedRequest.nationalityCode).isEqualTo(150)
        assertThat(capturedRequest.paymentType).isEqualTo(PaymentTypeDTO.BANK_TRANSFER)
        assertThat(capturedRequest.unionDue).isEqualTo(authorized)
        assertThat(capturedRequest.valueDate).isEqualTo(valueDate)
    }
}
